name: CI

on:
  workflow_dispatch:

  push:
    branches: [master, experimental]
    paths-ignore:
      # Case-sensitive paths to ignore on push events
      - '.editorconfig'
      - '.gitattributes'
      - '.gitignore'
      - 'MergeDbg.cmd'
      - 'TestCI.cmd'
      - '.github/codeql/codeql-config.yml'
      - '.github/ISSUE_TEMPLATE/**'
      - '.github/workflows/codeql.yml'
      - '.github/workflows/codespell.yml'
      - '.github/workflows/hash.yml'
      - '.github/workflows/lupdate.yml'
      - '.github/workflows/stale.yml'
      - '.github/workflows/test.yml'
      - '.github/workflows/winget.yml'
      - '.github/FUNDING.yml'
      - '.github/dependabot.yml'
      - 'Sandboxie/msgs/report/*.txt'
      - '**/*.md'
      - '**/*.html'
      - '**/*.png'
      - '**/*.bmp'
      - '**/LICENSE*'
      - '**/license*'
      - '**/README*'
      - '**/ReadMe*'
      - '**/INSTALL*'
      - '**/AUTHORS'
      - '**/COPYING'
  pull_request:
    branches: [master, experimental]
    paths-ignore:
      # Case-sensitive paths to ignore on pull events
      - '.editorconfig'
      - '.gitattributes'
      - '.gitignore'
      - 'MergeDbg.cmd'
      - 'TestCI.cmd'
      - '.github/codeql/codeql-config.yml'
      - '.github/ISSUE_TEMPLATE/**'
      - '.github/workflows/codeql.yml'
      - '.github/workflows/codespell.yml'
      - '.github/workflows/hash.yml'
      - '.github/workflows/lupdate.yml'
      - '.github/workflows/stale.yml'
      - '.github/workflows/test.yml'
      - '.github/workflows/winget.yml'
      - '.github/FUNDING.yml'
      - '.github/dependabot.yml'
      - 'Sandboxie/msgs/report/*.txt'
      - '**/*.md'
      - '**/*.html'
      - '**/*.png'
      - '**/*.bmp'
      - '**/LICENSE*'
      - '**/license*'
      - '**/README*'
      - '**/ReadMe*'
      - '**/INSTALL*'
      - '**/AUTHORS'
      - '**/COPYING'

env:
  forbuildVariables: use Installer\buildVariables.cmd file
  #qt_version: 5.15.16
  #qt6_version: 6.6.3
  #openssl_version: 3.4.0
  #ghSsl_user: xanasoft
  #ghSsl_repo: openssl-builds
  #ghQt6Win7_user: DavidXanatos
  #ghQt6Win7_repo: qtbase
  #ghQtBuilds_user: xanasoft
  #ghQtBuilds_repo: qt-builds
  #ghQtBuilds_hash_x86: 502e9a36a52918af4e116cd74c16c6c260d029087aaeee3775ab0e5d3f6a2705
  #ghQtBuilds_hash_x64: 673c288feeabd11ec66f9f454d49cde3945cbd3e3f71283b7a6c4df0893b19f2

jobs:
          
  Build_x64_Qt6:
    runs-on: windows-2022
    timeout-minutes: 45

    steps:
      - name: Checkout code
        uses: actions/checkout@v4.1.7

      - name: Load Variables from buildVariables.cmd
        shell: cmd
        run: |
          @echo on
          call "${{ github.workspace }}\Installer\buildVariables.cmd" build_qt6
          echo qt6_version=%qt6_version% >> %GITHUB_ENV%
          echo qt_version=%qt6_version% >> %GITHUB_ENV%

      - name: Setup msbuild
        uses: microsoft/setup-msbuild@v2

    #
    # Compile Sandboxie Core
    #

      - name: Build Sandboxie x86 (DLLs & svc)
        run: msbuild /t:build Sandboxie\SandboxDll.sln /p:Configuration="SbieRelease" /p:Platform=Win32 -maxcpucount:8

      - name: Build Sandboxie x64 (all)
        run: msbuild /t:build Sandboxie\Sandbox.sln /p:Configuration="SbieRelease" /p:Platform=x64 -maxcpucount:8
        
      - name: Build Sandboxie x64 (drv)
        run: msbuild /t:build Sandboxie\SandboxDrv.sln /p:Configuration="SbieRelease" /p:Platform=x64 -maxcpucount:8

    #
    # Prepare Qt Framework
    #

      - name: Install Qt6 x64
        uses: jurplel/install-qt-action@v4.3.0
        with:
      #    version: '6.2.4'
          version: '${{ env.qt6_version }}'
      #    dir: ..
      #    arch:  ${{ matrix.qt-target }}
          arch:  'win64_msvc2022_64'
      #    tools: 'tools_qtcreator,4.14.0-0-202012170949,qt.tools.qtcreator'
          tools: 'tools_opensslv3_x64'
          cache: true

#      - name: Install Qt5 x64
#        run: SandboxiePlus\install_qt.cmd x64

      - name: Installing Jom
      #  if: steps.cache-qt.outputs.cache-hit != 'true'
        run: SandboxiePlus\install_jom.cmd

    #
    # Compile Sandboxie Plus
    #

      - name: Build Sandboxie-Plus x64
        run: SandboxiePlus\qmake_plus.cmd x64 build_qt6

      - name: Build SbieShell x64
        run: msbuild /t:restore,build -p:RestorePackagesConfig=true SandboxiePlus\SbieShell\SbieShell.sln /p:Configuration="Release" /p:Platform=x64

    #
    # Compile Sandboxie Tools
    #

      - name: Build Sandboxie-Tools x64
        run: msbuild /t:build SandboxieTools\SandboxieTools.sln /p:Configuration="Release" /p:Platform=x64 -maxcpucount:8

    #
    # Merge everything together
    #

#      - name: Add Windows 7 compatible Qt6 DLLs
#        run: Installer\fix_qt6_win7.cmd

      - name: Add missing languages for Qt6 (issue 1528)
        run: Installer\fix_qt5_languages.cmd x64 build_qt6

      - name: Get openssl binaries
        run: Installer\get_openssl.cmd

      - name: Get 7z binaries
        run: Installer\get_7zip.cmd

      - name: Merging Build
        run: Installer\copy_build.cmd x64 build_qt6

      - name: Collect installer assets
        run: Installer\get_assets.cmd

      - name: Upload installer assets
        #if: github.ref == 'refs/heads/master' && github.event_name != 'pull_request'
        uses: actions/upload-artifact@v4.6.2
        with:
          name: Assets
          path: |
            Installer/Assets/*
          retention-days: 60

      - name: Upload Sandboxie x64
        #if: github.ref == 'refs/heads/master' && github.event_name != 'pull_request'
        uses: actions/upload-artifact@v4.6.2
        with:
          name: Sandboxie_x64
          path: |
            Installer/SbiePlus_x64/*
          retention-days: 60




  Build_ARM64_Qt6:
    runs-on: windows-2022
    timeout-minutes: 45

    steps:
      - name: Checkout code
        uses: actions/checkout@v4.1.7

      - name: Load Variables from buildVariables.cmd
        shell: cmd
        run: |
          @echo on
          call "${{ github.workspace }}\Installer\buildVariables.cmd" build_qt6
          echo qt6_version=%qt6_version% >> %GITHUB_ENV%

      - name: Setup msbuild
        uses: microsoft/setup-msbuild@v2

    #
    # Compile Sandboxie Core
    #

      - name: Build Sandboxie x86 (DLLs & svc)
        run: msbuild /t:build Sandboxie\SandboxDll.sln /p:Configuration="SbieRelease" /p:Platform=Win32 -maxcpucount:8

      - name: Build Sandboxie ARM64 (all)
        run: msbuild /t:build Sandboxie\Sandbox.sln /p:Configuration="SbieRelease" /p:Platform=ARM64 -maxcpucount:8

      - name: Build Sandboxie ARM64EC (DLL)
        run: msbuild /t:build Sandboxie\SandboxDll.sln /p:Configuration="SbieRelease" /p:Platform=ARM64EC -maxcpucount:8

      - name: Build Sandboxie ARM64 (drv)
        run: msbuild /t:build Sandboxie\SandboxDrv.sln /p:Configuration="SbieRelease" /p:Platform=ARM64 -maxcpucount:8

    #
    # Prepare Qt Framework for ARM64 (we also need Qt x64 for some utilities like qmake)
    #

      - name: Install Qt6 x64
        uses: jurplel/install-qt-action@v4.3.0
        with:
      #    version: '6.2.4'
          version: '${{ env.qt6_version }}'
      #    dir: ..
      #    arch:  ${{ matrix.qt-target }}
          arch:  'win64_msvc2022_64'
      #    tools: 'tools_qtcreator,4.14.0-0-202012170949,qt.tools.qtcreator'
          tools: 'tools_opensslv3_x64'
          cache: true

      - name: Install Qt6 ARM64
        uses: jurplel/install-qt-action@v4.3.0
        with:
      #    version: '6.2.4'
          version: '${{ env.qt6_version }}'
      #    dir: ..
      #    arch:  ${{ matrix.qt-target }}
          arch:  'win64_msvc2022_arm64_cross_compiled'
      #    tools: 'tools_qtcreator,4.14.0-0-202012170949,qt.tools.qtcreator'
          tools: 'tools_opensslv3_x64'
          cache: true

      - name: Installing Jom
      #  if: steps.cache-qt.outputs.cache-hit != 'true'
        run: SandboxiePlus\install_jom.cmd

    #
    # Compile Sandboxie Plus
    #

      - name: Build Sandboxie-Plus ARM64
        run: SandboxiePlus\qmake_plus.cmd ARM64 build_qt6

      - name: Build SbieShell ARM64
        run: msbuild /t:restore,build -p:RestorePackagesConfig=true SandboxiePlus\SbieShell\SbieShell.sln /p:Configuration="Release" /p:Platform=ARM64

    #
    # Compile Sandboxie Tools
    #

      - name: Build Sandboxie-Tools ARM64
        run: msbuild /t:build SandboxieTools\SandboxieTools.sln /p:Configuration="Release" /p:Platform=ARM64 -maxcpucount:8

    #
    # Merge everything together
    #

      - name: Add missing languages for Qt6 (issue 1528)
        run: Installer\fix_qt5_languages.cmd x64 build_qt6

      - name: Get openssl binaries
        run: Installer\get_openssl.cmd

      - name: Get 7z binaries
        run: Installer\get_7zip.cmd

      - name: Merging Build
        run: Installer\copy_build.cmd ARM64 build_qt6

      - name: Upload Sandboxie ARM64
        #if: github.ref == 'refs/heads/master' && github.event_name != 'pull_request'
        uses: actions/upload-artifact@v4.6.2
        with:
          name: Sandboxie_ARM64
          path: |
            Installer/SbiePlus_a64/*
          retention-days: 60
