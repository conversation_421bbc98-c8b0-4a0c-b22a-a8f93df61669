name: Publish to WinGet
on:
  release:
    types: [released]
jobs:
  publish-plus:
    runs-on: windows-latest # action can only be run on Windows
    steps:
      - name: Publish Sandboxie-Plus
        uses: vedantmgoyal9/winget-releaser@main
        with:
          identifier: Sandboxie.Plus
          installers-regex: "Sandboxie-Plus.*.exe$"
          token: ${{ secrets.WINGET_TOKEN }}

  publish-classic:
    runs-on: windows-latest
    steps:
      - name: Get Sandboxie-Classic version
        id: get_version
        run: |
          $VERSION="${{ github.event.release.name }}" -replace '^.*/ '
          "CLASSIC_VER=$VERSION" >> $env:GITHUB_OUTPUT
        shell: pwsh
      - name: Publish Sandboxie-Classic
        uses: vedantmgoyal9/winget-releaser@main
        with:
          version: ${{ steps.get_version.outputs.CLASSIC_VER }}
          identifier: Sandboxie.Classic
          installers-regex: "Sandboxie-Classic.*.exe$"
          token: ${{ secrets.WINGET_TOKEN }}
