//
// SbieControl.RC2 - resources Microsoft Visual C++ does not edit directly
//

#ifdef APSTUDIO_INVOKED
#error this file is not editable by Microsoft Visual C++
#endif //APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
// Add manually edited resources here...

/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION MY_VERSION_BINARY
 PRODUCTVERSION MY_VERSION_BINARY
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x40004L
 FILETYPE 0x2L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "Comments", "\0"
            VALUE "CompanyName", MY_COMPANY_NAME_STRING "\0"
            VALUE "FileDescription", MY_PRODUCT_NAME_STRING " Control\0"
            VALUE "FileVersion", MY_VERSION_STRING "\0"
   OPTIONAL_VALUE("InternalName", "Control\0")
            VALUE "LegalCopyright", MY_COPYRIGHT_STRING "\0"
            VALUE "LegalTrademarks", "\0"
   OPTIONAL_VALUE("OriginalFilename", "SbieCtrl.exe\0")
            VALUE "PrivateBuild", "\0"
            VALUE "ProductName", MY_PRODUCT_NAME_STRING "\0"
            VALUE "ProductVersion", MY_VERSION_STRING "\0"
            VALUE "SpecialBuild", "\0"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END

