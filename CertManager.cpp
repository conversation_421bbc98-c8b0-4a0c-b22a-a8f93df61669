#include <windows.h>
#include <iostream>
#include <string>
#include <vector>
#include <map>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <ctime>

// Certificate types
struct LicenseTemplate {
    std::string name;
    std::string displayName;
    std::string description;
    int validityDays;
    bool hasAllFeatures;
};

// Available license templates
std::vector<LicenseTemplate> getLicenseTemplates() {
    return {
        {"great_patreon", "Great Patreon", "Maximum features, hardware locked", 365, true},
        {"developer", "Developer", "Maximum features for development", 365, true},
        {"eternal", "Eternal", "Never expires, maximum features", -1, true},
        {"contributor", "Contributor", "Maximum features for contributors", 365, true},
        {"business", "Business", "Advanced features for business use", 365, false},
        {"personal", "Personal", "Standard features for personal use", 365, false},
        {"home", "Home", "Standard features for home use", 365, false},
        {"evaluation", "Evaluation", "30-day trial with all features", 30, true}
    };
}

// Get Sandboxie installation path
std::string getSandboxiePath() {
    HKEY hKey;
    char path[MAX_PATH] = {0};
    DWORD pathSize = sizeof(path);
    
    // Try to find Sandboxie-Plus installation
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SOFTWARE\\Sandboxie-Plus", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        if (RegQueryValueExA(hKey, "InstallationPath", nullptr, nullptr, (LPBYTE)path, &pathSize) == ERROR_SUCCESS) {
            RegCloseKey(hKey);
            return std::string(path);
        }
        RegCloseKey(hKey);
    }
    
    // Try classic Sandboxie
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SOFTWARE\\Sandboxie", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        if (RegQueryValueExA(hKey, "InstallationPath", nullptr, nullptr, (LPBYTE)path, &pathSize) == ERROR_SUCCESS) {
            RegCloseKey(hKey);
            return std::string(path);
        }
        RegCloseKey(hKey);
    }
    
    // Default paths
    std::vector<std::string> defaultPaths = {
        "C:\\Program Files\\Sandboxie-Plus",
        "C:\\Program Files (x86)\\Sandboxie-Plus",
        "C:\\Program Files\\Sandboxie",
        "C:\\Program Files (x86)\\Sandboxie"
    };
    
    for (const auto& defaultPath : defaultPaths) {
        if (GetFileAttributesA(defaultPath.c_str()) != INVALID_FILE_ATTRIBUTES) {
            return defaultPath;
        }
    }
    
    return "";
}

// Check if file exists
bool fileExists(const std::string& filename) {
    return GetFileAttributesA(filename.c_str()) != INVALID_FILE_ATTRIBUTES;
}

// Backup existing certificate
bool backupCertificate(const std::string& certPath) {
    if (!fileExists(certPath)) {
        return true; // No certificate to backup
    }
    
    time_t now = time(0);
    tm* ltm = localtime(&now);
    
    std::ostringstream backupName;
    backupName << certPath << ".backup." 
               << (ltm->tm_year + 1900) << "-"
               << std::setfill('0') << std::setw(2) << (ltm->tm_mon + 1) << "-"
               << std::setfill('0') << std::setw(2) << ltm->tm_mday << "-"
               << std::setfill('0') << std::setw(2) << ltm->tm_hour
               << std::setfill('0') << std::setw(2) << ltm->tm_min;
    
    return CopyFileA(certPath.c_str(), backupName.str().c_str(), FALSE) != 0;
}

// Install certificate
bool installCertificate(const std::string& templateName, const std::string& hwid = "") {
    std::string sandboxiePath = getSandboxiePath();
    if (sandboxiePath.empty()) {
        std::cerr << "Error: Could not find Sandboxie installation path." << std::endl;
        return false;
    }
    
    std::string certPath = sandboxiePath + "\\Certificate.dat";
    
    // Backup existing certificate
    if (!backupCertificate(certPath)) {
        std::cerr << "Warning: Could not backup existing certificate." << std::endl;
    }
    
    // Generate new certificate using LicenseGenerator
    std::string command = "LicenseGenerator.exe -t " + templateName + " -o \"" + certPath + "\"";
    if (!hwid.empty()) {
        command += " -h " + hwid;
    }
    
    int result = system(command.c_str());
    if (result != 0) {
        std::cerr << "Error: Failed to generate certificate." << std::endl;
        return false;
    }
    
    std::cout << "Certificate installed successfully!" << std::endl;
    std::cout << "Path: " << certPath << std::endl;
    
    return true;
}

// List installed certificates (backups)
void listBackups() {
    std::string sandboxiePath = getSandboxiePath();
    if (sandboxiePath.empty()) {
        std::cout << "Could not find Sandboxie installation path." << std::endl;
        return;
    }
    
    WIN32_FIND_DATAA findData;
    std::string searchPath = sandboxiePath + "\\Certificate.dat.backup.*";
    HANDLE hFind = FindFirstFileA(searchPath.c_str(), &findData);
    
    if (hFind == INVALID_HANDLE_VALUE) {
        std::cout << "No certificate backups found." << std::endl;
        return;
    }
    
    std::cout << "\nCertificate Backups:\n";
    std::cout << "===================\n";
    
    do {
        std::cout << findData.cFileName << std::endl;
    } while (FindNextFileA(hFind, &findData));
    
    FindClose(hFind);
}

// Restore certificate from backup
bool restoreCertificate(const std::string& backupName) {
    std::string sandboxiePath = getSandboxiePath();
    if (sandboxiePath.empty()) {
        std::cerr << "Error: Could not find Sandboxie installation path." << std::endl;
        return false;
    }
    
    std::string backupPath = sandboxiePath + "\\" + backupName;
    std::string certPath = sandboxiePath + "\\Certificate.dat";
    
    if (!fileExists(backupPath)) {
        std::cerr << "Error: Backup file not found: " << backupPath << std::endl;
        return false;
    }
    
    // Backup current certificate before restoring
    backupCertificate(certPath);
    
    if (CopyFileA(backupPath.c_str(), certPath.c_str(), FALSE)) {
        std::cout << "Certificate restored from backup: " << backupName << std::endl;
        return true;
    } else {
        std::cerr << "Error: Failed to restore certificate." << std::endl;
        return false;
    }
}

// Display current certificate info
void showCurrentCertificate() {
    std::string sandboxiePath = getSandboxiePath();
    if (sandboxiePath.empty()) {
        std::cout << "Could not find Sandboxie installation path." << std::endl;
        return;
    }
    
    std::string certPath = sandboxiePath + "\\Certificate.dat";
    
    if (!fileExists(certPath)) {
        std::cout << "No certificate installed." << std::endl;
        return;
    }
    
    std::ifstream file(certPath);
    if (!file.is_open()) {
        std::cerr << "Error: Could not read certificate file." << std::endl;
        return;
    }
    
    std::cout << "\nCurrent Certificate:\n";
    std::cout << "===================\n";
    
    std::string line;
    while (std::getline(file, line)) {
        if (line.find("SOFTWARE:") == 0 || 
            line.find("DATE:") == 0 || 
            line.find("TYPE:") == 0 || 
            line.find("OPTIONS:") == 0 ||
            line.find("HWID:") == 0) {
            std::cout << line << std::endl;
        }
    }
    
    file.close();
}

// Display usage
void displayUsage(const char* programName) {
    std::cout << "Sandboxie Certificate Manager\n";
    std::cout << "=============================\n\n";
    std::cout << "Usage: " << programName << " [command] [options]\n\n";
    std::cout << "Commands:\n";
    std::cout << "  install <template> [hwid]  Install certificate with template\n";
    std::cout << "  list                       List available templates\n";
    std::cout << "  current                    Show current certificate info\n";
    std::cout << "  backups                    List certificate backups\n";
    std::cout << "  restore <backup>           Restore from backup\n";
    std::cout << "  path                       Show Sandboxie installation path\n\n";
    std::cout << "Examples:\n";
    std::cout << "  " << programName << " list\n";
    std::cout << "  " << programName << " install great_patreon\n";
    std::cout << "  " << programName << " install business 1234567890ABCDEF\n";
    std::cout << "  " << programName << " restore Certificate.dat.backup.2024-01-01-1200\n\n";
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        displayUsage(argv[0]);
        return 1;
    }
    
    std::string command = argv[1];
    
    if (command == "list") {
        auto templates = getLicenseTemplates();
        std::cout << "\nAvailable License Templates:\n";
        std::cout << "============================\n";
        for (const auto& tmpl : templates) {
            std::cout << tmpl.name << " - " << tmpl.displayName << "\n";
            std::cout << "  " << tmpl.description;
            if (tmpl.validityDays == -1) {
                std::cout << " (Never expires)";
            } else {
                std::cout << " (" << tmpl.validityDays << " days)";
            }
            std::cout << "\n\n";
        }
    }
    else if (command == "install" && argc >= 3) {
        std::string templateName = argv[2];
        std::string hwid = (argc >= 4) ? argv[3] : "";
        installCertificate(templateName, hwid);
    }
    else if (command == "current") {
        showCurrentCertificate();
    }
    else if (command == "backups") {
        listBackups();
    }
    else if (command == "restore" && argc >= 3) {
        std::string backupName = argv[2];
        restoreCertificate(backupName);
    }
    else if (command == "path") {
        std::string path = getSandboxiePath();
        if (!path.empty()) {
            std::cout << "Sandboxie installation path: " << path << std::endl;
        } else {
            std::cout << "Sandboxie installation path not found." << std::endl;
        }
    }
    else {
        std::cerr << "Unknown command: " << command << std::endl;
        displayUsage(argv[0]);
        return 1;
    }
    
    return 0;
}
