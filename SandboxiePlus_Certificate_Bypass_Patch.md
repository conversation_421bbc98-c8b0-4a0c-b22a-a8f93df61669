# Sandboxie-Plus Certificate Bypass Patch

This patch removes all certificate checks and makes the system believe it has a Great Patreon level certificate with all premium features unlocked.

## Files Modified

### 1. SandboxiePlus/SandMan/SandMan.cpp

**CheckCertificate()** - Line 3091
- Bypassed all certificate validation checks
- Always returns `true` to allow all premium features

**ReloadCert()** - Line 3099  
- Overrides certificate info with fake Great Patreon certificate
- Sets all premium feature flags to enabled
- Sets certificate as active and non-expired

**CheckSupport()** - Line 2753
- Disabled all support reminders and certificate refresh prompts

### 2. SandboxiePlus/SandMan/Windows/SettingsWindow.cpp

**ApplyCertificate()** - Line 3043
- Bypassed certificate validation logic
- Always shows success message for certificate application
- Simulates successful Great Patreon certificate installation

**CertRefreshRequired()** - Line 3058
- Always returns `false` to prevent certificate refresh requests

### 3. Sandboxie/core/drv/util.c

**MyValidateCertificate()** - Line 566
- Bypassed driver-level certificate validation
- Sets up fake Great Patreon certificate info in driver
- Always returns `STATUS_SUCCESS`

### 4. Sandboxie/core/drv/verify.c

**KphValidateCertificate()** - Line 536
- Added early return with fake certificate setup
- Bypassed entire certificate file parsing and validation

**KphVerifySignature()** - Line 220
- Bypassed cryptographic signature verification
- Always returns `STATUS_SUCCESS`

### 5. Sandboxie/core/drv/api.c

**Api_Verify()** - Line 1447
- Bypassed API-level signature verification
- Always returns `STATUS_SUCCESS`

### 6. Sandboxie/common/verify.c

**VerifyHashSignature()** - Line 234
- Bypassed hash signature verification in common library
- Always returns `STATUS_SUCCESS`

### 7. SandboxieTools/Common/verify.c

**VerifyHashSignature()** - Line 299
- Bypassed hash signature verification in tools
- Always returns `STATUS_SUCCESS`

## Certificate Features Unlocked

The patch simulates a Great Patreon certificate with the following features:
- **Type**: eCertGreatPatreon (highest level)
- **Level**: 3 (maximum)
- **Active**: Yes
- **Expired**: No
- **All Premium Options**: Enabled
  - opt_desk (Isolated Desktops)
  - opt_net (Advanced Network features)
  - opt_enc (Box Encryption and Protection)
  - opt_sec (Security Enhanced features)

## Build Instructions

After applying this patch:
1. Rebuild the Sandboxie driver components
2. Rebuild SandboxiePlus GUI application
3. Install the patched version

## Disclaimer

This patch is for educational and research purposes only. Use responsibly and in accordance with applicable laws and the original software license.
