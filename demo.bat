@echo off
echo Sandboxie License Tools Demo
echo ============================
echo.

REM Check if tools are built
if not exist "bin\LicenseGenerator.exe" (
    echo ERROR: Tools not built. Please run build.bat first.
    pause
    exit /b 1
)

echo 1. Listing available license templates...
echo.
bin\LicenseGenerator.exe --list
echo.
pause

echo 2. Generating a Great Patreon certificate...
echo.
bin\LicenseGenerator.exe -t great_patreon -o demo_great_patreon.dat
echo.
pause

echo 3. Generating an Eternal certificate...
echo.
bin\LicenseGenerator.exe -t eternal -o demo_eternal.dat
echo.
pause

echo 4. Generating a Business certificate with hardware lock...
echo.
bin\LicenseGenerator.exe -t business -h 1234567890ABCDEF1234567890ABCDEF -o demo_business_locked.dat
echo.
pause

echo 5. Displaying certificate contents...
echo.
echo Great Patreon Certificate:
echo --------------------------
bin\TestApp.exe --show-cert demo_great_patreon.dat
echo.
pause

echo Eternal Certificate:
echo --------------------
bin\TestApp.exe --show-cert demo_eternal.dat
echo.
pause

echo Business Locked Certificate:
echo -----------------------------
bin\TestApp.exe --show-cert demo_business_locked.dat
echo.
pause

echo 6. Testing Certificate Manager...
echo.
echo Available templates:
bin\CertManager.exe list
echo.
pause

echo 7. Showing current Sandboxie certificate (if any)...
echo.
bin\CertManager.exe current
echo.
pause

echo 8. Showing Sandboxie installation path...
echo.
bin\CertManager.exe path
echo.
pause

echo Demo completed!
echo.
echo Generated demo certificates:
echo - demo_great_patreon.dat
echo - demo_eternal.dat  
echo - demo_business_locked.dat
echo.
echo To install a certificate to Sandboxie, use:
echo bin\CertManager.exe install [template_name]
echo.
echo To test API interception, use:
echo bin\TestApp.exe --test-api
echo.
echo See LICENSE_TOOLS_README.md for complete documentation.

pause
