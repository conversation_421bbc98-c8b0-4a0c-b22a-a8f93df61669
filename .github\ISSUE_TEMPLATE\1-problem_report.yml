name: Problem report
description: Please report your problem here to help us improve.
labels: ["Confirmation Pending"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to open this report!
        Before you begin, please use the GitHub search bar to see if your problem has already been reported.

        Also, you may find your answer:
        1. in the [sandboxie-docs](https://github.com/sandboxie-plus/sandboxie-docs) repository (currently there are [synchronization issues](https://github.com/sandboxie-plus/Sandboxie/discussions/1756) with sandboxie-plus.com)
        2. in the other [support channels](https://github.com/sandboxie-plus/Sandboxie/discussions/1768#discussioncomment-2503401)
        3. in the [cached copy](https://github.com/Sandboxie-Website-Archive/sandboxie-website-archive.github.io) of the old Sandboxie forum: `site:https://sandboxie-website-archive.github.io/www.sandboxie.com/old-forums/`
        4. in the [contributing guidelines](https://github.com/sandboxie-plus/Sandboxie/blob/master/CONTRIBUTING.md)
  - type: textarea
    id: what-happened
    attributes:
      label: Describe what you noticed and did
      description: |
        It is highly recommended to write the exact steps to reproduce the behavior.

        Please feel free to attach screenshots, screen recordings and links to help explain it.
      placeholder: |
        Describe as accurately as possible what you saw and did!
        1. Go to '....'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
    validations:
      required: true
  - type: input
    id: prevalence
    attributes:
      label: How often did you encounter it so far?
      description: If applicable, write more details about its frequency.
      placeholder: e.g. Whenever I ...
    validations:
      required: false
  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected behavior
      description: A clear and concise description of what you expect to see.
      placeholder: Tell us what the default behavior should be!
    validations:
      required: true
  - type: input
    id: sandboxed-program
    attributes:
      label: Affected program
      description: |
        If applicable, in which sandboxed program did you see the problem?

        If not, you can type something like "Not relevant".
      placeholder: e.g. Firefox 121.0 64-bit, ....
    validations:
      required: true
  - type: input
    id: download-link
    attributes:
      label: Download link
      description: |
        If you had a compatibility issue, it is highly recommended to include a download link to the incompatible software.

        If not, you can type something like "Not relevant" or "Not available".
      placeholder: https://
    validations:
      required: true
  - type: dropdown
    id: sandboxed-or-not
    attributes:
      label: Where is the program located?
      description: Select "Not relevant" if you don't have any malfunctioning program to report.
      options:
        - "The program is installed both inside and outside the sandbox."
        - "The program is installed only outside the sandbox."
        - "The program is installed only inside a sandbox (NOT in the real system anyway)."
        - "I tried to install it only inside a sandbox, but I wasn't able to achieve it."
        - "Not relevant to my request."
    validations:
      required: true
  - type: dropdown
    id: crash-or-not
    attributes:
      label: Did the program or any related process close unexpectedly?
      description: In case of doubt, please take a look at the [crash dump locations](#description-crashdumps) below.
      multiple: true
      options:
        - "Yes, it did. See the link to the .dmp file(s) I have attached here."
        - "Yes, it did. I'm going to share the .dmp file(s) in a later comment."
        - "Yes, it did, but I accidentally removed / lost the .dmp file(s)."
        - "Yes, it did, but no .dmp file has been created in the system."
        - "Yes, it did, but I don't want to share the .dmp file(s) for privacy reasons."
        - "Yes, it did, but I don't know how to attach the .dmp file(s)."
        - "No, not at all."
    validations:
      required: true
  - type: input
    id: crashdumps
    attributes:
      label: Crash dump
      description: |
        List of crash dump locations to check out:
        - C:\\Users\\<USER>\\AppData\\Local\\CrashDumps
        - C:\\Windows\\Minidump
        - C:\\Sandbox\\%User%\\DefaultBox\\user\\current\\AppData\\Local\\CrashDumps
        - C:\\Sandbox\\%USER%\\%SANDBOX% (only if you previously set EnableMiniDump=y in the sandbox section of your Sandboxie.ini file)
        - C:\\Windows\\MEMORY.DMP (known as %SystemRoot%\\MEMORY.DMP)

        If applicable, provide a direct link containing the .dmp file(s) for your crash issue!
        See also: [Generate a kernel or complete crash dump](https://learn.microsoft.com/en-us/troubleshoot/windows-client/performance/generate-a-kernel-or-complete-crash-dump) | [Driver Verifier - tracking down a misbehaving driver](https://answers.microsoft.com/en-us/windows/forum/windows_10-update/driver-verifier-tracking-down-a-mis-behaving/f5cb4faf-556b-4b6d-95b3-c48669e4c983).

        Tip: You can upload the .dmp file(s) on a file storage service, then share the resulting link below.
      placeholder: https://
  - type: input
    id: sandboxie-version
    attributes:
      label: What version of Sandboxie are you running now?
      placeholder: e.g. Sandboxie Plus 1.12.6 64-bit
    validations:
      required: true
  - type: dropdown
    id: installation-types
    attributes:
      label: Is it a new installation of Sandboxie?
      options:
        - "I recently did a new clean installation."
        - "I just updated Sandboxie from a previous version (I don't remember which one)."
        - "I just updated Sandboxie from a previous version (I remember which one it is)."
        - "I recently upgraded it from an older version than 1.5.3 / 5.60.3."
        - "I have been using the same version for some time."
    validations:
      required: true
  - type: input
    id: regression
    attributes:
      label: Is it a regression from previous versions?
      description: If you intend to test multiple versions, specify in which Sandboxie version this problem was introduced the first time (including any pre-release version).
      placeholder: e.g. The issue was introduced the first time in 5.62.1 64-bit
  - type: dropdown
    id: box-types
    attributes:
      label: In which sandbox type you have this problem?
      description: Select "Not relevant" if you don't have any malfunctioning program or setting to report.
      options:
        - "In a standard isolation sandbox (yellow sandbox icon)."
        - "In a hardened sandbox with data protection (red sandbox icon)."
        - "In a security hardened sandbox (orange sandbox icon)."
        - "In a sandbox with data protection (blue sandbox icon)."
        - "In an Application Compartment sandbox with data protection (cyan sandbox icon)."
        - "In an Application Compartment sandbox with no isolation (green sandbox icon)."
        - "In an encrypted sandbox (black sandbox icon)."
        - "All sandbox types (I tried them all)."
        - "I only reproduced it with Sandboxie Classic."
        - "Not relevant to my request."
    validations:
      required: true
  - type: dropdown
    id: box-state
    attributes:
      label: Can you reproduce this problem on a new empty sandbox?
      description: |
        Select "Not relevant" if you don't have any malfunctioning program to report.

        Tip: A new empty sandbox is a new box created without any installed program or data.
      options:
      - "I can confirm it also on a new empty sandbox."
      - "My sandbox contains existing programs or data."
      - "Not relevant to my request."
    validations:
      required: true
  - type: input
    id: windows-version
    attributes:
      label: What is your Windows edition and version?
      placeholder: "e.g. Windows 10 Pro 22H2 64-bit"
    validations:
      required: true
  - type: dropdown
    id: user-account
    attributes:
      label: "In which Windows account you have this problem?"
      description: "Please note that built-in user accounts like Guest or Administrator are disabled by default in Windows because they pose a security risk."
      multiple: true
      options:
        - "A local account (Standard user)."
        - "A local account (Administrator)."
        - "A Microsoft account (Standard user)."
        - "A Microsoft account (Administrator)."
        - "An account with secure desktop turned off for UAC prompts."
        - "An account with UAC protection completely turned off."
        - "An account with UAC protection set to Always notify."
        - "I use the built-in Administrator account."
        - "I use the built-in Administrator with Admin Approval Mode turned on."
        - "I reproduced it under a Guest account."
        - "Not relevant to my request."
    validations:
      required: true
  - type: input
    id: security-software
    attributes:
      label: Please mention any installed security software
      description: |
        Please note that third-party security solutions can still conflict even when their real-time protection is turned off, so you may want to consider a temporary uninstall with reboot to rule out any issue with Sandboxie. See also: [Reporting issues to third-party vendors in case of evident conflicts](https://github.com/sandboxie-plus/Sandboxie/issues/2025#issuecomment-**********).
      placeholder: e.g. Microsoft Defender Antivirus
    validations:
      required: true
  - type: textarea
    id: policy-settings
    attributes:
      label: Did you previously enable some security policy settings outside Sandboxie?
      description: |
        For example in gpedit.msc, secpol.msc, Windows Defender Exploit protection settings, management tools like parental controls, Microsoft Intune, etc.

        Tip: You may consider to turn off non-default Windows settings.
      placeholder: |
        e.g. I enabled the following security / policy settings outside Sandboxie:
        ....
  - type: input
    id: logs
    attributes:
      label: Trace log
      description: |
        Please consider to take one or more logs for Resource Access issues. For instructions, see [Sandboxie Trace](https://git.io/Jwj2y).

        Tip: You can compress multiple log files in a .zip archive to upload on a file storage service, then share the resulting link below.
      placeholder: https://
  - type: textarea
    id: sandboxie-config
    attributes:
      label: Sandboxie.ini configuration
      description: |
        If applicable, consider to attach your Sandboxie.ini settings (usually located in `C:\Windows` or in the installation folder).
      placeholder: |
        [GlobalSettings]
        .....

        [UserSettings_xxxxxxxx]
        .....

        [DefaultBox]
        .....

      render: ini
