# Sandboxie License Generator and API Interceptor

This project provides a complete license management system for Sandboxie, including:

1. **License Generator** - Creates valid Sandboxie certificates
2. **API Interceptor** - Hooks license verification functions
3. **Certificate Manager** - Manages and installs certificates
4. **Test Application** - Tests the license system functionality

## Features

- Generate certificates for all Sandboxie license types
- Bypass signature verification through API hooking
- Easy certificate installation and management
- Support for hardware-locked certificates
- Backup and restore functionality
- Interactive testing interface

## License Types Supported

| Type | Description | Features | Validity |
|------|-------------|----------|----------|
| `great_patreon` | Great Patreon | All features, hardware locked | 365 days |
| `developer` | Developer | All features | 365 days |
| `eternal` | Eternal | All features | Never expires |
| `contributor` | Contributor | All features | 365 days |
| `business` | Business | Advanced features | 365 days |
| `personal` | Personal | Standard features | 365 days |
| `home` | Home | Standard features | 365 days |
| `evaluation` | Evaluation | All features | 30 days |

## Building

### Prerequisites

- MinGW-w64 or Visual Studio
- Microsoft Detours library (for API interceptor)
- Windows SDK

### Compilation

```bash
# Build all tools
make all

# Build individual tools
make LicenseGenerator.exe
make APIInterceptor.dll
make CertManager.exe
make TestApp.exe

# Install to bin directory
make install

# Clean build files
make clean
```

## Usage

### 1. License Generator

Generate certificates for different license types:

```bash
# List available templates
LicenseGenerator.exe --list

# Generate Great Patreon certificate
LicenseGenerator.exe -t great_patreon -o Certificate.dat

# Generate hardware-locked business certificate
LicenseGenerator.exe -t business -h 1234567890ABCDEF -o BusinessCert.dat

# Generate eternal license (never expires)
LicenseGenerator.exe -t eternal -o EternalCert.dat
```

### 2. Certificate Manager

Manage Sandboxie certificates:

```bash
# List available templates
CertManager.exe list

# Install Great Patreon certificate
CertManager.exe install great_patreon

# Install hardware-locked certificate
CertManager.exe install business 1234567890ABCDEF

# Show current certificate
CertManager.exe current

# List certificate backups
CertManager.exe backups

# Restore from backup
CertManager.exe restore Certificate.dat.backup.2024-01-01-1200

# Show Sandboxie installation path
CertManager.exe path
```

### 3. API Interceptor

The API interceptor DLL automatically hooks license verification functions when loaded. It can be used programmatically:

```cpp
// Load the DLL
HMODULE hDll = LoadLibrary(L"APIInterceptor.dll");

// Get function pointers
SetLicenseType_t SetLicenseType = (SetLicenseType_t)GetProcAddress(hDll, "SetLicenseType");
GetCertificateInfo_t GetCertificateInfo = (GetCertificateInfo_t)GetProcAddress(hDll, "GetCertificateInfo");

// Set license type
SetLicenseType("great_patreon");

// Get certificate info
SCertInfo* certInfo = GetCertificateInfo();
```

### 4. Test Application

Test the license system:

```bash
# Test API interceptor with different license types
TestApp.exe --test-api

# Display certificate file contents
TestApp.exe --show-cert Certificate.dat
```

## Certificate File Format

Sandboxie certificates use a simple text format:

```
SOFTWARE: Sandboxie-Plus
DATE: 01.01.2024 +365
TYPE: GREAT_PATREON-ADVANCED
OPTIONS: SBOX,EBOX,NETI,DESK
HWID: 1234567890ABCDEF1234567890ABCDEF
UPDATEKEY: ABCDEF1234567890ABCDEF1234567890
SIGNATURE: [Base64 encoded signature]
```

### Fields Explanation

- **SOFTWARE**: Must be "Sandboxie-Plus"
- **DATE**: Issue date in DD.MM.YYYY format, optionally +days for validity
- **TYPE**: License type, optionally with level (TYPE-LEVEL)
- **OPTIONS**: Comma-separated feature flags (SBOX,EBOX,NETI,DESK)
- **HWID**: Hardware ID for locked certificates (optional)
- **UPDATEKEY**: Update key for certificate validation
- **SIGNATURE**: Base64 encoded signature (bypassed by interceptor)

### Feature Flags

- **SBOX**: Security enhanced box types
- **EBOX**: Box encryption and protection
- **NETI**: Advanced network features
- **DESK**: Isolated Sandboxie desktops

## Installation

1. Build all tools using the Makefile
2. Copy the executables to your desired location
3. Use CertManager to install certificates directly to Sandboxie
4. Or manually copy Certificate.dat to your Sandboxie installation directory

## How It Works

### License Generator
- Creates properly formatted certificate files
- Generates dummy signatures (bypassed by patches)
- Supports all Sandboxie license types and levels
- Can create hardware-locked certificates

### API Interceptor
- Uses Microsoft Detours to hook license verification functions
- Bypasses signature verification by always returning success
- Provides fake certificate information based on selected license type
- Can be controlled programmatically through exported functions

### Certificate Manager
- Automatically detects Sandboxie installation path
- Creates backups before installing new certificates
- Provides easy switching between license types
- Integrates with the license generator

## Security Note

This tool is for educational and testing purposes. The API interceptor bypasses Sandboxie's license verification by:

1. Hooking `KphValidateCertificate()` to return fake certificate data
2. Hooking `KphVerifySignature()` to always return success
3. Hooking `KphVerifyBuffer()` to bypass signature verification

## Troubleshooting

### Common Issues

1. **"Could not find Sandboxie installation"**
   - Ensure Sandboxie is installed
   - Check registry entries for installation path
   - Manually specify path if needed

2. **"Failed to generate certificate"**
   - Ensure LicenseGenerator.exe is in PATH
   - Check write permissions to target directory
   - Verify template name is correct

3. **"API interceptor not working"**
   - Ensure APIInterceptor.dll is in the same directory
   - Check that Detours library is properly linked
   - Verify target process architecture matches DLL

### Debug Mode

Enable debug output by setting environment variable:
```bash
set SBIE_DEBUG=1
```

## License

This project is provided for educational purposes only. Use responsibly and in accordance with applicable laws and software licenses.
