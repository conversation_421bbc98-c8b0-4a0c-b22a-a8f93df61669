Sandboxie Plus - Complete Build Package
=======================================

This is a fully compiled build of Sandboxie Plus with all necessary components.

CONTENTS:
---------
- SandMan.exe           - Main Sandboxie Plus UI (Qt-based modern interface)
- SbieCtrl.exe         - Classic Sandboxie Control Panel
- SbieSvc.exe          - Sandboxie Service
- SbieDll.dll          - Core Sandboxie injection DLL
- Start.exe            - Command-line sandbox launcher
- Qt5*.dll             - Qt runtime libraries
- msvcp140.dll         - Visual C++ runtime
- vcruntime140*.dll    - Visual C++ runtime
- platforms/           - Qt platform plugins
- Templates/           - Sandbox templates (empty)
- Various COM helpers  - SandboxieBITS.exe, SandboxieCrypto.exe, etc.

INSTALLATION:
------------
1. ENABLE TEST SIGNING MODE (REQUIRED):
   - Open Command Prompt as Administrator
   - Run: bcdedit /set testsigning on
   - Restart your computer
   - You should see "Test Mode" in the bottom-right corner after reboot

2. EXTRACT TO DESIRED LOCATION:
   - Copy this entire folder to your preferred location (e.g., C:\Sandboxie)
   - Do NOT run from Downloads folder for long-term use

3. INITIAL SETUP:
   - Right-click SandMan.exe and "Run as administrator"
   - Go to Maintenance menu and click "Install Service"
   - The driver installation may require additional steps

USAGE:
------
- Double-click "Install_and_Run.bat" for guided startup
- Or run SandMan.exe directly as Administrator
- For classic interface: run SbieCtrl.exe
- For command line: Start.exe [program_to_sandbox]

TROUBLESHOOTING:
---------------
- If SandMan.exe won't start: Check that test signing is enabled
- If driver issues persist: The driver (SbieDrv.sys) may need to be built separately
- Missing DLL errors: Make sure all files are in the same directory
- Permission errors: Always run as Administrator initially

NOTES:
------
- This is a DEBUG build and includes debug symbols (.pdb files)
- For production use, you may want to remove .pdb files to save space
- The driver component may need additional signing for full functionality
- Built with Visual Studio 2022 and Qt 5.15.16

Built on: August 9, 2025
Architecture: x64
Configuration: Release
