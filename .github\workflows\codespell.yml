# This is a basic workflow to help you get started with Actions

name: Spell check

# Controls when the workflow will run
on:
  workflow_call:
  schedule:
    # Trigger Codespell at a scheduled time
    # * is a special character in YAML so you have to quote this string
    - cron: '30 0 * * *'
  # Triggers the workflow on push or pull request events
 # push:
  #  branches:
  #  - 'master'
  pull_request:
    branches:
  #  - 'master'
    - '*typo*'
    - '*spell*'

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "Codespell"
  Codespell:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Check spelling errors
        run: |
          pip install codespell
          wget -q https://raw.githubusercontent.com/codespell-project/codespell/master/codespell_lib/data/dictionary.txt
          wget -q https://raw.githubusercontent.com/codespell-project/codespell/master/codespell_lib/data/dictionary_rare.txt
          wget -q https://raw.githubusercontent.com/codespell-project/codespell/master/codespell_lib/data/dictionary_code.txt
          # Add further typos for continuous checks
          echo 'behavioure->behaviour' >> dictionary.txt
          echo 'blockign->blocking' >> dictionary.txt
          echo 'ate->are' >> dictionary.txt
          echo 'linve->live' >> dictionary.txt
          echo 'actuallly->actually' >> dictionary.txt
          echo 'asynchroniusly->asynchronously' >> dictionary.txt
          echo 'synchroniusly->synchronously' >> dictionary.txt
          echo 'seams->seems' >> dictionary.txt
          echo 'selelction->selection' >> dictionary.txt
          echo 'injectted->injected' >> dictionary.txt
          echo 'prepanding->prepending' >> dictionary.txt
          echo 'depanding->depending' >> dictionary.txt
          echo 'undependant->independent' >> dictionary.txt
          echo 'probablys->probably' >> dictionary.txt
          echo 'ware->were' >> dictionary.txt
          echo 'contetx->context' >> dictionary.txt
          echo 'isseu->issue' >> dictionary.txt
          echo 'issuw->issue' >> dictionary.txt
          echo 'compatybility->compatibility' >> dictionary.txt
          echo 'incompatybility->incompatibility' >> dictionary.txt
          echo 'compatybilities->compatibilities' >> dictionary.txt
          echo 'incompatybilities->incompatibilities' >> dictionary.txt
          echo 'logn->long' >> dictionary.txt
          echo 'postion->position' >> dictionary.txt
          echo 'trase->trace' >> dictionary.txt
          echo 'eider->either' >> dictionary.txt
          echo 'isoaltion->isolation' >> dictionary.txt
          echo 'symbolicl->symbolic' >> dictionary.txt
          echo 're enable->re-enable' >> dictionary.txt
          echo 'foder->folder' >> dictionary.txt
          echo 'fodername1->foldername1' >> dictionary.txt
          echo 'fodler->folder' >> dictionary.txt
          echo 'built in->built-in' >> dictionary.txt
          echo 'tigether->together' >> dictionary.txt
          echo 'auxyliary->auxiliary' >> dictionary.txt
          echo 'squating->squatting' >> dictionary.txt
          echo 'suppoter->supporter' >> dictionary.txt
          echo 'routime->routine' >> dictionary.txt
          echo 'explorere->explorer' >> dictionary.txt
          echo 'mein->main' >> dictionary.txt
          echo 'trigegred->triggered' >> dictionary.txt
          echo 'windoe->window' >> dictionary.txt
          echo 'hread->thread' >> dictionary.txt
          echo 'tamplete->template' >> dictionary.txt
          echo 'prising->praising' >> dictionary.txt
          echo 'wi->we, will, ' >> dictionary.txt
          echo 'dayly->daily' >> dictionary.txt
          echo 'erdinal->ordinal' >> dictionary.txt
          echo 'wizars->wizard' >> dictionary.txt
          echo 'pined->pinned' >> dictionary.txt
          echo 'inidcate->indicate' >> dictionary.txt
          echo 'unsoecific->unspecific' >> dictionary.txt
          echo 'soecific->specific' >> dictionary.txt
          echo 'cpecific->specific' >> dictionary.txt
          echo 'incomatible->incompatible' >> dictionary.txt
          echo 'maliciouse->malicious' >> dictionary.txt
          echo 'strickt->strict' >> dictionary.txt
          echo 'brak->break' >> dictionary.txt
          echo 'booth->both' >> dictionary.txt
          echo 'vcertificate->certificate' >> dictionary.txt
          echo 'fron->front' >> dictionary.txt
          echo 'ahndling->handling' >> dictionary.txt
          echo 'previwe->preview' >> dictionary.txt
          echo 'variouse->various' >> dictionary.txt
          echo 'resumeed->resumed' >> dictionary.txt
          echo 'autoamtic->automatic' >> dictionary.txt
          echo 'connencted->connected' >> dictionary.txt
          echo 'querygin->querying' >> dictionary.txt
          echo 'soem->some' >> dictionary.txt
          echo 'previosue->previous' >> dictionary.txt
          echo 'bfore->before' >> dictionary.txt
          echo 'lettet->letter' >> dictionary.txt
          echo 'readly->readily' >> dictionary.txt
          echo 'wes->we' >> dictionary.txt
          echo 'fluch->flush' >> dictionary.txt
          echo 'crome->Chrome' >> dictionary_code.txt
          echo 'chromim->Chromium' >> dictionary_code.txt
          echo 'kmdutill->kmdutil' >> dictionary_code.txt
          echo 'ancrypted->encrypted' >> dictionary_code.txt
          echo 'hadrware->hardware' >> dictionary_code.txt
          echo 'sandbocie->Sandboxie' >> dictionary_code.txt
          echo 'sanboxie->Sandboxie' >> dictionary_code.txt
          echo "sandboxies->Sandboxie's, sandboxed, " >> dictionary_code.txt
          echo 'reparese->reparse' >> dictionary_code.txt
          echo 'tailing->trailing' >> dictionary_code.txt
          echo 'hoocked->hooked' >> dictionary_code.txt
          # Only lowercase letters are allowed in --ignore-words-list
          codespell --dictionary=dictionary.txt --dictionary=dictionary_rare.txt --dictionary=dictionary_code.txt \
          --ignore-words-list="wil,unknwn,tolen,pevent,doubleclick,parm,parms,etcp,ois,ba,ptd,modell,namesd,stdio,uint,errorstring,ontext,atend,deque,ecounter,nmake,namess,inh,daa,varient,lite,uis,emai,ws,slanguage,woh,tne,typpos,enew,shft,seh,ser,servent,socio-economic,rime,falt,infor,vor,lets,od,fo,aas,shs,focusin,readded," \
          --skip="./.git,./.github/workflows/codespell.yml,./dictionary*.txt,./Sandboxie/msgs/Text-*-*.txt,./Sandboxie/msgs/report/Report-*.txt,./SandboxiePlus/SandMan/*.ts,./Installer/Languages.iss,./Installer/isl/*.isl,./SandboxiePlus/SandMan/Troubleshooting/lang_*.json,./Sandboxie/install/build.bat,./SandboxieTools/ImBox/dc/crypto_fast/xts_fast.c"
