# Makefile for Sandboxie License Tools

CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2
LDFLAGS = -lbcrypt -lws2_32

# Targets
TARGETS = LicenseGenerator.exe APIInterceptor.dll CertManager.exe TestApp.exe

all: $(TARGETS)

# License Generator
LicenseGenerator.exe: LicenseGenerator.cpp
	$(CXX) $(CXXFLAGS) -o $@ $< $(LDFLAGS)

# API Interceptor DLL
APIInterceptor.dll: APIInterceptor.cpp
	$(CXX) $(CXXFLAGS) -shared -o $@ $< $(LDFLAGS) -Wl,--out-implib,APIInterceptor.lib

# Certificate Manager
CertManager.exe: CertManager.cpp
	$(CXX) $(CXXFLAGS) -o $@ $< $(LDFLAGS) -lgdi32 -luser32 -lcomctl32

# Test Application
TestApp.exe: TestApp.cpp
	$(CXX) $(CXXFLAGS) -o $@ $< $(LDFLAGS)

clean:
	del /Q *.exe *.dll *.lib *.o 2>nul || true

install: all
	@echo Installing license tools...
	@if not exist "bin" mkdir bin
	copy *.exe bin\
	copy *.dll bin\
	@echo Installation complete!

.PHONY: all clean install
