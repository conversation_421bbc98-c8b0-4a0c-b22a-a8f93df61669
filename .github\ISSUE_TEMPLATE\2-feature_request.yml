name: Feature request
description: Suggest a new idea for Sandboxie.
labels: ["Feature Request"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to open this request!
        Before you begin, please use the GitHub search bar to see if your feature request has already been reported.

        Also, you may find your answer:
        1. in the [sandboxie-docs](https://github.com/sandboxie-plus/sandboxie-docs) repository (currently there are [synchronization issues](https://github.com/sandboxie-plus/Sandboxie/discussions/1756) with sandboxie-plus.com)
        2. in the other [support channels](https://github.com/sandboxie-plus/Sandboxie/discussions/1768#discussioncomment-2503401)
        3. in the [cached copy](https://github.com/Sandboxie-Website-Archive/sandboxie-website-archive.github.io) of the old Sandboxie forum: `site:https://sandboxie-website-archive.github.io/www.sandboxie.com/old-forums/`
        4. in the [contributing guidelines](https://github.com/sandboxie-plus/Sandboxie/blob/master/CONTRIBUTING.md)
  - type: textarea
    id: describe-feature
    attributes:
      label: Is your feature request related to a problem or use case?
      description: A clear and concise description of what the problem is. You can add screenshots and provide links to help explain it.
    validations:
      required: true
  - type: textarea
    id: preferred-solution
    attributes:
      label: Describe the solution you'd like
      description: A clear and concise description of what you want to happen.
    validations:
      required: true
  - type: textarea
    id: alternatives
    attributes:
      label: Describe alternatives you've considered
      description: If applicable, specify any alternative solutions or features you've considered.
    validations:
      required: false
