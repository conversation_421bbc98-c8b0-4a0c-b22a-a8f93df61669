@echo off
echo Building Sandboxie License Tools...
echo ===================================

REM Create bin directory if it doesn't exist
if not exist "bin" mkdir bin

echo.
echo Building License Generator...
g++ -std=c++17 -Wall -Wextra -O2 -o bin\LicenseGenerator.exe LicenseGenerator.cpp -lbcrypt -lws2_32
if %errorlevel% neq 0 (
    echo ERROR: Failed to build License Generator
    pause
    exit /b 1
)

echo Building Certificate Manager...
g++ -std=c++17 -Wall -Wextra -O2 -o bin\CertManager.exe CertManager.cpp -lbcrypt -lws2_32 -lgdi32 -luser32 -lcomctl32
if %errorlevel% neq 0 (
    echo ERROR: Failed to build Certificate Manager
    pause
    exit /b 1
)

echo Building Test Application...
g++ -std=c++17 -Wall -Wextra -O2 -o bin\TestApp.exe TestApp.cpp -lbcrypt -lws2_32
if %errorlevel% neq 0 (
    echo ERROR: Failed to build Test Application
    pause
    exit /b 1
)

echo Building API Interceptor...
REM Note: This requires Microsoft Detours library
REM You may need to install Detours and adjust the path
g++ -std=c++17 -Wall -Wextra -O2 -shared -o bin\APIInterceptor.dll APIInterceptor.cpp -lbcrypt -lws2_32 -Wl,--out-implib,bin\APIInterceptor.lib
if %errorlevel% neq 0 (
    echo WARNING: Failed to build API Interceptor (Detours library may be missing)
    echo This is optional - other tools will still work
)

echo.
echo Build completed successfully!
echo.
echo Files created in bin\ directory:
dir bin\*.exe bin\*.dll 2>nul

echo.
echo Usage Examples:
echo ===============
echo bin\LicenseGenerator.exe --list
echo bin\CertManager.exe list
echo bin\TestApp.exe --help
echo.
echo See LICENSE_TOOLS_README.md for detailed documentation.

pause
