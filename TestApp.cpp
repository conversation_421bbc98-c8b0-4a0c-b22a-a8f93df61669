#include <windows.h>
#include <iostream>
#include <string>
#include <iomanip>
#include <fstream>

// Certificate types from Sandboxie
enum ECertType {
    eCertNoType         = 0b00000,
    eCertEternal        = 0b00100,
    eCertContributor    = 0b00101,
    eCertBusiness       = 0b01000,
    eCertPersonal       = 0b01100,
    eCertHome           = 0b10000,
    eCertFamily         = 0b10001,
    eCertDeveloper      = 0b10100,
    eCertPatreon        = 0b11000,
    eCertGreatPatreon   = 0b11001,
    eCertEntryPatreon   = 0b11010,
    eCertEvaluation     = 0b11100
};

enum ECertLevel {
    eCertNoLevel        = 0b000,
    eCertStandard       = 0b010,
    eCertStandard2      = 0b011,
    eCertAdvanced1      = 0b100,
    eCertAdvanced       = 0b101,
    eCertMaxLevel       = 0b111,
};

// Certificate info structure (matches Sandboxie's SCertInfo)
typedef union _SCertInfo {
    unsigned long long State;
    struct {
        unsigned long
            active      : 1,    // certificate is active
            expired     : 1,    // certificate is expired but may be active
            outdated    : 1,    // certificate is expired, not anymore valid for the current build
            reservd_1   : 2,    // DEPRECATED
            grace_period: 1,    // the certificate is expired and or outdated but we keep it valid for 1 extra month
            locked      : 1,
            lock_req    : 1,

            type        : 5,
            level       : 3,

            reservd_3   : 8,

            reservd_4   : 4,    // More features
            opt_desk    : 1,    // Isolated Sandboxie Desktops
            opt_net     : 1,    // Advanced Network features
            opt_enc     : 1,    // Box Encryption and Box Protection
            opt_sec     : 1;    // Various security enhanced box types

        long expirers_in_sec;
    };
} SCertInfo;

// Function pointers for API interceptor
typedef BOOL (*SetLicenseType_t)(const char* licenseType);
typedef const char* (*GetLicenseType_t)();
typedef SCertInfo* (*GetCertificateInfo_t)();
typedef BOOL (*EnableInterception_t)();
typedef BOOL (*DisableInterception_t)();

// Convert certificate type to string
std::string certTypeToString(unsigned long type) {
    switch (type) {
        case eCertNoType: return "No Type";
        case eCertEternal: return "Eternal";
        case eCertContributor: return "Contributor";
        case eCertBusiness: return "Business";
        case eCertPersonal: return "Personal";
        case eCertHome: return "Home";
        case eCertFamily: return "Family";
        case eCertDeveloper: return "Developer";
        case eCertPatreon: return "Patreon";
        case eCertGreatPatreon: return "Great Patreon";
        case eCertEntryPatreon: return "Entry Patreon";
        case eCertEvaluation: return "Evaluation";
        default: return "Unknown (" + std::to_string(type) + ")";
    }
}

// Convert certificate level to string
std::string certLevelToString(unsigned long level) {
    switch (level) {
        case eCertNoLevel: return "No Level";
        case eCertStandard: return "Standard";
        case eCertStandard2: return "Standard 2";
        case eCertAdvanced1: return "Advanced 1";
        case eCertAdvanced: return "Advanced";
        case eCertMaxLevel: return "Maximum";
        default: return "Unknown (" + std::to_string(level) + ")";
    }
}

// Display certificate information
void displayCertificateInfo(const SCertInfo& cert) {
    std::cout << "\nCertificate Information:\n";
    std::cout << "========================\n";
    std::cout << "State: 0x" << std::hex << cert.State << std::dec << "\n";
    std::cout << "Active: " << (cert.active ? "Yes" : "No") << "\n";
    std::cout << "Expired: " << (cert.expired ? "Yes" : "No") << "\n";
    std::cout << "Outdated: " << (cert.outdated ? "Yes" : "No") << "\n";
    std::cout << "Grace Period: " << (cert.grace_period ? "Yes" : "No") << "\n";
    std::cout << "Locked: " << (cert.locked ? "Yes" : "No") << "\n";
    std::cout << "Lock Required: " << (cert.lock_req ? "Yes" : "No") << "\n";
    std::cout << "Type: " << certTypeToString(cert.type) << " (" << cert.type << ")\n";
    std::cout << "Level: " << certLevelToString(cert.level) << " (" << cert.level << ")\n";
    
    std::cout << "\nFeatures:\n";
    std::cout << "  Desktop Isolation: " << (cert.opt_desk ? "Enabled" : "Disabled") << "\n";
    std::cout << "  Network Features: " << (cert.opt_net ? "Enabled" : "Disabled") << "\n";
    std::cout << "  Encryption: " << (cert.opt_enc ? "Enabled" : "Disabled") << "\n";
    std::cout << "  Security: " << (cert.opt_sec ? "Enabled" : "Disabled") << "\n";
    
    if (cert.expirers_in_sec == -1) {
        std::cout << "Expires: Never\n";
    } else if (cert.expirers_in_sec > 0) {
        int days = cert.expirers_in_sec / (24 * 3600);
        std::cout << "Expires in: " << days << " days (" << cert.expirers_in_sec << " seconds)\n";
    } else {
        std::cout << "Expires in: " << cert.expirers_in_sec << " seconds (expired)\n";
    }
}

// Test API interceptor
void testAPIInterceptor() {
    std::cout << "Testing API Interceptor...\n";
    std::cout << "==========================\n";
    
    // Load the API interceptor DLL
    HMODULE hDll = LoadLibraryA("APIInterceptor.dll");
    if (!hDll) {
        std::cerr << "Error: Could not load APIInterceptor.dll" << std::endl;
        return;
    }
    
    // Get function pointers
    SetLicenseType_t SetLicenseType = (SetLicenseType_t)GetProcAddress(hDll, "SetLicenseType");
    GetLicenseType_t GetLicenseType = (GetLicenseType_t)GetProcAddress(hDll, "GetLicenseType");
    GetCertificateInfo_t GetCertificateInfo = (GetCertificateInfo_t)GetProcAddress(hDll, "GetCertificateInfo");
    EnableInterception_t EnableInterception = (EnableInterception_t)GetProcAddress(hDll, "EnableInterception");
    DisableInterception_t DisableInterception = (DisableInterception_t)GetProcAddress(hDll, "DisableInterception");
    
    if (!SetLicenseType || !GetLicenseType || !GetCertificateInfo || !EnableInterception || !DisableInterception) {
        std::cerr << "Error: Could not get function pointers from APIInterceptor.dll" << std::endl;
        FreeLibrary(hDll);
        return;
    }
    
    // Test different license types
    std::vector<std::string> licenseTypes = {
        "great_patreon", "developer", "eternal", "business", "personal", "evaluation"
    };
    
    for (const auto& licenseType : licenseTypes) {
        std::cout << "\n" << std::string(50, '=') << "\n";
        std::cout << "Testing License Type: " << licenseType << "\n";
        std::cout << std::string(50, '=') << "\n";
        
        if (SetLicenseType(licenseType.c_str())) {
            std::cout << "License type set successfully.\n";
            std::cout << "Current license type: " << GetLicenseType() << "\n";
            
            SCertInfo* certInfo = GetCertificateInfo();
            if (certInfo) {
                displayCertificateInfo(*certInfo);
            } else {
                std::cerr << "Error: Could not get certificate info" << std::endl;
            }
        } else {
            std::cerr << "Error: Could not set license type" << std::endl;
        }
        
        std::cout << "\nPress Enter to continue...";
        std::cin.get();
    }
    
    FreeLibrary(hDll);
}

// Read and display certificate file
void displayCertificateFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Error: Could not open certificate file: " << filename << std::endl;
        return;
    }
    
    std::cout << "\nCertificate File Contents:\n";
    std::cout << "==========================\n";
    
    std::string line;
    while (std::getline(file, line)) {
        // Skip signature line for readability
        if (line.find("SIGNATURE:") != 0) {
            std::cout << line << std::endl;
        } else {
            std::cout << "SIGNATURE: [Base64 encoded signature]" << std::endl;
        }
    }
    
    file.close();
}

// Display usage
void displayUsage(const char* programName) {
    std::cout << "Sandboxie License Test Application\n";
    std::cout << "==================================\n\n";
    std::cout << "Usage: " << programName << " [options]\n\n";
    std::cout << "Options:\n";
    std::cout << "  --test-api           Test API interceptor with different license types\n";
    std::cout << "  --show-cert <file>   Display certificate file contents\n";
    std::cout << "  --help               Show this help message\n\n";
    std::cout << "Examples:\n";
    std::cout << "  " << programName << " --test-api\n";
    std::cout << "  " << programName << " --show-cert Certificate.dat\n\n";
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        displayUsage(argv[0]);
        return 1;
    }
    
    std::string arg = argv[1];
    
    if (arg == "--help") {
        displayUsage(argv[0]);
        return 0;
    }
    else if (arg == "--test-api") {
        testAPIInterceptor();
    }
    else if (arg == "--show-cert" && argc >= 3) {
        displayCertificateFile(argv[2]);
    }
    else {
        std::cerr << "Unknown argument: " << arg << std::endl;
        displayUsage(argv[0]);
        return 1;
    }
    
    return 0;
}
