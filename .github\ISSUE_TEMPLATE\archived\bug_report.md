---
name: Problem report
about: Please report your problem here to help us improve
title: ''
labels: ''
assignees: ''

---

**Describe the problem**
A clear and concise description of what the problem is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Expected behavior**
A clear and concise description of what you expected to happen.

**System details and installed software**
 - What is your Windows edition and version? (e.g. Windows 10 Pro 20H2 64-bit)
 - What is your current Sandboxie edition and version? (e.g. Sandboxie Plus 0.9.6 64-bit)
 - What was your previous Sandboxie version before the update, if any?
 - Please mention in which version this bug got introduced, because it acts as a guide for testers and developers.
 - Please mention any security software running in the background, even if the real-time protection is disabled.

**If you had a compatibility issue or a crash**
 - Please add a download link to the program not working properly in Sandboxie.
 - Please consider to take a log and make it available for analysis: https://git.io/Jwj2y
 - Please attach the actual memory dump and NOT the simple output, which often turns out to be insufficient for a proper analysis.

**Additional context**
Add any other context about the problem here.

**Sandboxie configuration**
If applicable, consider to attach your Sandboxie.ini configuration by copying the content on https://gist.github.com and sharing the resulting link. If you decide to paste the configuration here, make sure to use the backticks around strings, like in this working example:

<details>

<summary>My initial sandboxie.ini settings</summary>

```

[GlobalSettings]

.....

[UserSettings_175D0429]

.....

[DefaultBox]

.....

```

</details>
