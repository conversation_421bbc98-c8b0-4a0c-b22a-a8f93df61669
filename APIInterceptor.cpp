#include <windows.h>
#include <iostream>
#include <string>
#include <map>
#include <detours.h>

#pragma comment(lib, "detours.lib")

// Certificate types from Sandboxie
enum ECertType {
    eCertNoType         = 0b00000,
    eCertEternal        = 0b00100,
    eCertContributor    = 0b00101,
    eCertBusiness       = 0b01000,
    eCertPersonal       = 0b01100,
    eCertHome           = 0b10000,
    eCertFamily         = 0b10001,
    eCertDeveloper      = 0b10100,
    eCertPatreon        = 0b11000,
    eCertGreatPatreon   = 0b11001,
    eCertEntryPatreon   = 0b11010,
    eCertEvaluation     = 0b11100
};

enum ECertLevel {
    eCertNoLevel        = 0b000,
    eCertStandard       = 0b010,
    eCertStandard2      = 0b011,
    eCertAdvanced1      = 0b100,
    eCertAdvanced       = 0b101,
    eCertMaxLevel       = 0b111,
};

// Certificate info structure (matches Sandboxie's SCertInfo)
typedef union _SCertInfo {
    unsigned long long State;
    struct {
        unsigned long
            active      : 1,    // certificate is active
            expired     : 1,    // certificate is expired but may be active
            outdated    : 1,    // certificate is expired, not anymore valid for the current build
            reservd_1   : 2,    // DEPRECATED
            grace_period: 1,    // the certificate is expired and or outdated but we keep it valid for 1 extra month
            locked      : 1,
            lock_req    : 1,

            type        : 5,
            level       : 3,

            reservd_3   : 8,

            reservd_4   : 4,    // More features
            opt_desk    : 1,    // Isolated Sandboxie Desktops
            opt_net     : 1,    // Advanced Network features
            opt_enc     : 1,    // Box Encryption and Box Protection
            opt_sec     : 1;    // Various security enhanced box types

        long expirers_in_sec;
    };
} SCertInfo;

// Global certificate configuration
SCertInfo g_FakeCertInfo = { 0 };
std::string g_CurrentLicenseType = "great_patreon";

// Function pointers for original functions
typedef NTSTATUS (WINAPI *KphValidateCertificate_t)();
typedef NTSTATUS (WINAPI *KphVerifySignature_t)(PVOID Hash, ULONG HashSize, PUCHAR Signature, ULONG SignatureSize);
typedef NTSTATUS (WINAPI *KphVerifyBuffer_t)(PUCHAR Buffer, ULONG BufferSize, PUCHAR Signature, ULONG SignatureSize);

KphValidateCertificate_t OriginalKphValidateCertificate = nullptr;
KphVerifySignature_t OriginalKphVerifySignature = nullptr;
KphVerifyBuffer_t OriginalKphVerifyBuffer = nullptr;

// Initialize fake certificate based on license type
void InitializeFakeCertificate(const std::string& licenseType) {
    g_FakeCertInfo.State = 0; // Clear all fields
    
    // Set common fields
    g_FakeCertInfo.active = 1;
    g_FakeCertInfo.expired = 0;
    g_FakeCertInfo.outdated = 0;
    g_FakeCertInfo.grace_period = 0;
    g_FakeCertInfo.expirers_in_sec = 365 * 24 * 3600; // 1 year
    
    if (licenseType == "great_patreon") {
        g_FakeCertInfo.type = eCertGreatPatreon;
        g_FakeCertInfo.level = eCertMaxLevel;
        g_FakeCertInfo.locked = 1;
        g_FakeCertInfo.lock_req = 0;
        g_FakeCertInfo.opt_desk = 1;
        g_FakeCertInfo.opt_net = 1;
        g_FakeCertInfo.opt_enc = 1;
        g_FakeCertInfo.opt_sec = 1;
    }
    else if (licenseType == "developer") {
        g_FakeCertInfo.type = eCertDeveloper;
        g_FakeCertInfo.level = eCertMaxLevel;
        g_FakeCertInfo.locked = 0;
        g_FakeCertInfo.lock_req = 0;
        g_FakeCertInfo.opt_desk = 1;
        g_FakeCertInfo.opt_net = 1;
        g_FakeCertInfo.opt_enc = 1;
        g_FakeCertInfo.opt_sec = 1;
    }
    else if (licenseType == "eternal") {
        g_FakeCertInfo.type = eCertEternal;
        g_FakeCertInfo.level = eCertMaxLevel;
        g_FakeCertInfo.locked = 0;
        g_FakeCertInfo.lock_req = 0;
        g_FakeCertInfo.opt_desk = 1;
        g_FakeCertInfo.opt_net = 1;
        g_FakeCertInfo.opt_enc = 1;
        g_FakeCertInfo.opt_sec = 1;
        g_FakeCertInfo.expirers_in_sec = -1; // Never expires
    }
    else if (licenseType == "business") {
        g_FakeCertInfo.type = eCertBusiness;
        g_FakeCertInfo.level = eCertAdvanced;
        g_FakeCertInfo.locked = 0;
        g_FakeCertInfo.lock_req = 0;
        g_FakeCertInfo.opt_desk = 0;
        g_FakeCertInfo.opt_net = 1;
        g_FakeCertInfo.opt_enc = 1;
        g_FakeCertInfo.opt_sec = 1;
    }
    else if (licenseType == "personal") {
        g_FakeCertInfo.type = eCertPersonal;
        g_FakeCertInfo.level = eCertStandard;
        g_FakeCertInfo.locked = 0;
        g_FakeCertInfo.lock_req = 0;
        g_FakeCertInfo.opt_desk = 0;
        g_FakeCertInfo.opt_net = 0;
        g_FakeCertInfo.opt_enc = 0;
        g_FakeCertInfo.opt_sec = 1;
    }
    else {
        // Default to evaluation
        g_FakeCertInfo.type = eCertEvaluation;
        g_FakeCertInfo.level = eCertMaxLevel;
        g_FakeCertInfo.locked = 0;
        g_FakeCertInfo.lock_req = 0;
        g_FakeCertInfo.opt_desk = 1;
        g_FakeCertInfo.opt_net = 1;
        g_FakeCertInfo.opt_enc = 1;
        g_FakeCertInfo.opt_sec = 1;
        g_FakeCertInfo.expirers_in_sec = 30 * 24 * 3600; // 30 days
    }
}

// Hooked certificate validation function
NTSTATUS WINAPI HookedKphValidateCertificate() {
    // Always return success and set up fake certificate
    InitializeFakeCertificate(g_CurrentLicenseType);
    return 0; // STATUS_SUCCESS
}

// Hooked signature verification function
NTSTATUS WINAPI HookedKphVerifySignature(PVOID Hash, ULONG HashSize, PUCHAR Signature, ULONG SignatureSize) {
    // Always return success to bypass signature verification
    return 0; // STATUS_SUCCESS
}

// Hooked buffer verification function
NTSTATUS WINAPI HookedKphVerifyBuffer(PUCHAR Buffer, ULONG BufferSize, PUCHAR Signature, ULONG SignatureSize) {
    // Always return success to bypass buffer verification
    return 0; // STATUS_SUCCESS
}

// Install hooks
BOOL InstallHooks() {
    DetourTransactionBegin();
    DetourUpdateThread(GetCurrentThread());
    
    // Try to find and hook the certificate validation functions
    HMODULE hSandboxie = GetModuleHandleA("SbieDrv.sys");
    if (!hSandboxie) {
        hSandboxie = GetModuleHandleA("SbieApi.dll");
    }
    
    if (hSandboxie) {
        OriginalKphValidateCertificate = (KphValidateCertificate_t)GetProcAddress(hSandboxie, "KphValidateCertificate");
        OriginalKphVerifySignature = (KphVerifySignature_t)GetProcAddress(hSandboxie, "KphVerifySignature");
        OriginalKphVerifyBuffer = (KphVerifyBuffer_t)GetProcAddress(hSandboxie, "KphVerifyBuffer");
        
        if (OriginalKphValidateCertificate) {
            DetourAttach(&(PVOID&)OriginalKphValidateCertificate, HookedKphValidateCertificate);
        }
        if (OriginalKphVerifySignature) {
            DetourAttach(&(PVOID&)OriginalKphVerifySignature, HookedKphVerifySignature);
        }
        if (OriginalKphVerifyBuffer) {
            DetourAttach(&(PVOID&)OriginalKphVerifyBuffer, HookedKphVerifyBuffer);
        }
    }
    
    LONG error = DetourTransactionCommit();
    return (error == NO_ERROR);
}

// Remove hooks
BOOL RemoveHooks() {
    DetourTransactionBegin();
    DetourUpdateThread(GetCurrentThread());
    
    if (OriginalKphValidateCertificate) {
        DetourDetach(&(PVOID&)OriginalKphValidateCertificate, HookedKphValidateCertificate);
    }
    if (OriginalKphVerifySignature) {
        DetourDetach(&(PVOID&)OriginalKphVerifySignature, HookedKphVerifySignature);
    }
    if (OriginalKphVerifyBuffer) {
        DetourDetach(&(PVOID&)OriginalKphVerifyBuffer, HookedKphVerifyBuffer);
    }
    
    LONG error = DetourTransactionCommit();
    return (error == NO_ERROR);
}

// Exported functions for controlling the interceptor
extern "C" {
    __declspec(dllexport) BOOL SetLicenseType(const char* licenseType) {
        if (licenseType) {
            g_CurrentLicenseType = licenseType;
            InitializeFakeCertificate(g_CurrentLicenseType);
            return TRUE;
        }
        return FALSE;
    }
    
    __declspec(dllexport) const char* GetLicenseType() {
        return g_CurrentLicenseType.c_str();
    }
    
    __declspec(dllexport) SCertInfo* GetCertificateInfo() {
        return &g_FakeCertInfo;
    }
    
    __declspec(dllexport) BOOL EnableInterception() {
        return InstallHooks();
    }
    
    __declspec(dllexport) BOOL DisableInterception() {
        return RemoveHooks();
    }
}

// DLL entry point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        DisableThreadLibraryCalls(hModule);
        InitializeFakeCertificate(g_CurrentLicenseType);
        InstallHooks();
        break;
    case DLL_PROCESS_DETACH:
        RemoveHooks();
        break;
    }
    return TRUE;
}
