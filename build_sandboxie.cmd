@echo off
echo Building Sandboxie Core Components...

set MSBUILD="C:\Program Files\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe"

echo Building Sandboxie x86 DLLs and svc...
%MSBUILD% /t:build Sandboxie\SandboxDll.sln /p:Configuration=SbieRelease /p:Platform=Win32 /verbosity:minimal
if %ERRORLEVEL% neq 0 (
    echo Failed to build Sandboxie x86 components
    exit /b 1
)

echo Building Sandboxie x64 (all)...
%MSBUILD% /t:build Sandboxie\Sandbox.sln /p:Configuration=SbieRelease /p:Platform=x64 /verbosity:minimal
if %ERRORLEVEL% neq 0 (
    echo Failed to build Sandboxie x64 components
    exit /b 1
)

echo Building Sandboxie x64 (drv)...
%MSBUILD% /t:build Sandboxie\SandboxDrv.sln /p:Configuration=SbieRelease /p:Platform=x64 /verbosity:minimal
if %ERRORLEVEL% neq 0 (
    echo Failed to build Sandboxie x64 driver
    exit /b 1
)

echo Sandboxie core components built successfully!
