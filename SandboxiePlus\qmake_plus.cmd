call "%~dp0..\Installer\buildVariables.cmd" %*

REM @echo off
REM echo Current dir: %cd%
REM echo folder: %~dp0
REM echo arch: %1
REM echo qt_version: %2
REM echo qt6_version: %3

REM echo %*
REM IF "%~3" == "" ( set "qt6_version=6.3.1" ) ELSE ( set "qt6_version=%~3" )
REM IF "%~2" == "" ( set "qt_version=5.15.16" ) ELSE ( set "qt_version=%~2" )
 
IF %1 == Win32 (
  set qt_path=%~dp0..\..\Qt\%qt_version%\msvc2022

  REM get private headers for QtCore
  Xcopy /E /I /Y /Q %~dp0..\..\Qt\%qt_version%\msvc2022\include\QtCore\%qt_version%\QtCore %~dp0..\..\Qt\%qt_version%\msvc2022\include\QtCore
  
  set build_arch=Win32
  set qt_params= 
call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars32.bat"
)
IF %1 == x64 (
  set qt_path=%~dp0..\..\Qt\%qt_version%\msvc2022_64
REM  set qt_path=%~dp0..\..\Qt\%qt6_version%\msvc2022_64
  
  REM get private headers for QtCore
  Xcopy /E /I /Y /Q %~dp0..\..\Qt\%qt_version%\msvc2022_64\include\QtCore\%qt_version%\QtCore %~dp0..\..\Qt\%qt_version%\msvc2022_64\include\QtCore
REM  Xcopy /E /I /Y /Q %~dp0..\..\Qt\%qt6_version%\msvc2022_64\include\QtCore\%qt6_version%\QtCore %~dp0..\..\Qt\%qt6_version%\msvc2022_64\include\QtCore
  
  set build_arch=x64
  set qt_params= 
  call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
)
IF %1 == ARM64 (
  set qt_path=%~dp0..\..\Qt\%qt6_version%\msvc2022_64
  
  REM get private headers for QtCore
  Xcopy /E /I /Y /Q %~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\include\QtCore\%qt6_version%\QtCore %~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\include\QtCore
  
  set build_arch=ARM64
  
REM  set qt_params=-qtconf "%~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\bin\target_qt.conf"
  
REM type %~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\bin\target_qt.conf
  
  REM
  REM The target_qt.conf as provided by the windows-2022 github action runner
  REM is non functional, hence we create our own working edition here.
  REM
  
  echo [DevicePaths] > %~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\bin\my_target_qt.conf
  echo Prefix=C:/Qt/Qt-%qt6_version% >> %~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\bin\my_target_qt.conf
  echo [Paths] >> %~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\bin\my_target_qt.conf
  echo Prefix=../ >> %~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\bin\my_target_qt.conf
  echo HostPrefix=../../msvc2022_64 >> %~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\bin\my_target_qt.conf
  echo HostData=../msvc2022_arm64 >> %~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\bin\my_target_qt.conf
  echo Sysroot= >> %~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\bin\my_target_qt.conf
  echo SysrootifyPrefix=false >> %~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\bin\my_target_qt.conf
  echo TargetSpec=win32-arm64-msvc >> %~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\bin\my_target_qt.conf
  echo HostSpec=win32-msvc >> %~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\bin\my_target_qt.conf
  echo Documentation=../../Docs/Qt-%qt6_version% >> %~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\bin\my_target_qt.conf
  echo Examples=../../Examples/Qt-%qt6_version% >> %~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\bin\my_target_qt.conf
  
  set qt_params=-qtconf "%~dp0..\..\Qt\%qt6_version%\msvc2022_arm64\bin\my_target_qt.conf"
  
REM  set VSCMD_DEBUG=3
call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvarsamd64_arm64.bat"
)
@echo on



mkdir %~dp0\Build_UGlobalHotkey_%build_arch%
cd %~dp0\Build_UGlobalHotkey_%build_arch%

%qt_path%\bin\qmake.exe %~dp0\UGlobalHotkey\uglobalhotkey.qc.pro %qt_params%
%~dp0..\..\Qt\Tools\QtCreator\bin\jom.exe -f Makefile.Release -j 8
IF %ERRORLEVEL% NEQ 0 goto :error
if NOT EXIST %~dp0\bin\%build_arch%\Release\UGlobalHotkey.dll goto :error



mkdir %~dp0\Build_qtsingleapp_%build_arch%
cd %~dp0\Build_qtsingleapp_%build_arch%

%qt_path%\bin\qmake.exe %~dp0\QtSingleApp\qtsingleapp\qtsingleapp\qtsingleapp.qc.pro %qt_params%
%~dp0..\..\Qt\Tools\QtCreator\bin\jom.exe -f Makefile.Release -j 8
IF %ERRORLEVEL% NEQ 0 goto :error
if NOT EXIST %~dp0\bin\%build_arch%\Release\qtsingleapp.dll goto :error



mkdir %~dp0\Build_MiscHelpers_%build_arch%
cd %~dp0\Build_MiscHelpers_%build_arch%

%qt_path%\bin\qmake.exe %~dp0\MiscHelpers\MiscHelpers.qc.pro %qt_params%
%~dp0..\..\Qt\Tools\QtCreator\bin\jom.exe -f Makefile.Release -j 8
IF %ERRORLEVEL% NEQ 0 goto :error
if NOT EXIST %~dp0\bin\%build_arch%\Release\MiscHelpers.dll goto :error



mkdir %~dp0\Build_QSbieAPI_%build_arch%
cd %~dp0\Build_QSbieAPI_%build_arch%

%qt_path%\bin\qmake.exe %~dp0\QSbieAPI\QSbieAPI.qc.pro %qt_params%
%~dp0..\..\Qt\Tools\QtCreator\bin\jom.exe -f Makefile.Release -j 8
IF %ERRORLEVEL% NEQ 0 goto :error
if NOT EXIST %~dp0\bin\%build_arch%\Release\QSbieAPI.dll goto :error



mkdir %~dp0\Build_SandMan_%build_arch%
cd %~dp0\Build_SandMan_%build_arch%

if "%qt_version:~0,1%" == "5" ( 
    %qt_path%\bin\qmake.exe %~dp0\SandMan\SandMan.qc.pro %qt_params%
)
if "%qt_version:~0,1%" == "6" ( 
    %qt_path%\bin\qmake.exe %~dp0\SandMan\SandMan-Qt6.qc.pro %qt_params%
)
%~dp0..\..\Qt\Tools\QtCreator\bin\jom.exe -f Makefile.Release -j 8
IF %ERRORLEVEL% NEQ 0 goto :error
if NOT EXIST %~dp0\bin\%build_arch%\Release\SandMan.exe goto :error



cd %~dp0

rem dir .\bin
rem dir .\bin\%build_arch%
rem dir .\bin\%build_arch%\Release

goto :eof

:error
echo Build failed
exit 1
