@echo off
echo ==========================================
echo    Sandboxie Plus - Complete Build
echo ==========================================
echo.
echo This is a complete build of Sandboxie Plus with all components.
echo.
echo IMPORTANT SETUP INSTRUCTIONS:
echo.
echo 1. Enable Windows Test Mode (required for unsigned drivers):
echo    Run as Administrator: bcdedit /set testsigning on
echo    Then restart your computer
echo.
echo 2. The following components are included:
echo    - SandMan.exe (Main Sandboxie Plus UI)
echo    - SbieSvc.exe (Sandboxie Service)
echo    - SbieDll.dll (Sandboxie DLL for injection)
echo    - Start.exe (Sandboxie launcher)
echo    - SbieCtrl.exe (Classic Sandboxie Control)
echo    - All Qt runtime libraries
echo.
echo 3. To install and start Sandboxie:
echo    - Run SandMan.exe as Administrator
echo    - Use the Maintenance menu to install components
echo.
echo Press any key to start SandMan.exe...
pause >nul

echo Starting Sandboxie Plus...
start SandMan.exe

echo.
echo If SandMan.exe fails to start:
echo - Make sure Windows is in test signing mode
echo - Run as Administrator
echo - Check that all DLL files are present
echo.
echo For troubleshooting, you can also try:
echo - SbieCtrl.exe (Classic UI)
echo - Start.exe [program] (Direct sandbox launch)
echo.
pause
