#include <windows.h>
#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <map>
#include <iomanip>
#include <sstream>
#include <ctime>
#include <bcrypt.h>
#include <ntstatus.h>

#pragma comment(lib, "bcrypt.lib")

// Certificate types from Sandboxie
enum ECertType {
    eCertNoType         = 0b00000,
    eCertEternal        = 0b00100,
    eCertContributor    = 0b00101,
    eCertBusiness       = 0b01000,
    eCertPersonal       = 0b01100,
    eCertHome           = 0b10000,
    eCertFamily         = 0b10001,
    eCertDeveloper      = 0b10100,
    eCertPatreon        = 0b11000,
    eCertGreatPatreon   = 0b11001,
    eCertEntryPatreon   = 0b11010,
    eCertEvaluation     = 0b11100
};

enum ECertLevel {
    eCertNoLevel        = 0b000,
    eCertStandard       = 0b010,
    eCertStandard2      = 0b011,
    eCertAdvanced1      = 0b100,
    eCertAdvanced       = 0b101,
    eCertMaxLevel       = 0b111,
};

struct CertificateInfo {
    ECertType type;
    ECertLevel level;
    std::string typeStr;
    std::string levelStr;
    std::string options;
    int days;
    std::string hwid;
    std::string updateKey;
    std::string software;
    bool locked;
};

// Base64 encoding/decoding functions
static const std::string base64_chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    "abcdefghijklmnopqrstuvwxyz"
    "0123456789+/";

std::string base64_encode(unsigned char const* bytes_to_encode, unsigned int in_len) {
    std::string ret;
    int i = 0;
    int j = 0;
    unsigned char char_array_3[3];
    unsigned char char_array_4[4];

    while (in_len--) {
        char_array_3[i++] = *(bytes_to_encode++);
        if (i == 3) {
            char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
            char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
            char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
            char_array_4[3] = char_array_3[2] & 0x3f;

            for(i = 0; (i <4) ; i++)
                ret += base64_chars[char_array_4[i]];
            i = 0;
        }
    }

    if (i) {
        for(j = i; j < 3; j++)
            char_array_3[j] = '\0';

        char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
        char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
        char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
        char_array_4[3] = char_array_3[2] & 0x3f;

        for (j = 0; (j < i + 1); j++)
            ret += base64_chars[char_array_4[j]];

        while((i++ < 3))
            ret += '=';
    }

    return ret;
}

// Generate a dummy signature (since we're bypassing verification anyway)
std::string generateDummySignature() {
    // Generate 64 bytes of dummy data
    unsigned char dummy_sig[64];
    for (int i = 0; i < 64; i++) {
        dummy_sig[i] = (unsigned char)(rand() % 256);
    }
    return base64_encode(dummy_sig, 64);
}

// Get current date in DD.MM.YYYY format
std::string getCurrentDate() {
    time_t now = time(0);
    tm* ltm = localtime(&now);

    std::ostringstream oss;
    oss << std::setfill('0') << std::setw(2) << ltm->tm_mday << "."
        << std::setfill('0') << std::setw(2) << (ltm->tm_mon + 1) << "."
        << (ltm->tm_year + 1900);

    return oss.str();
}

// Generate HWID (dummy for now)
std::string generateHWID() {
    std::ostringstream oss;
    for (int i = 0; i < 32; i++) {
        oss << std::hex << (rand() % 16);
    }
    return oss.str();
}

// Generate Update Key (dummy for now)
std::string generateUpdateKey() {
    std::ostringstream oss;
    for (int i = 0; i < 32; i++) {
        oss << std::hex << (rand() % 16);
    }
    return oss.str();
}

// Predefined certificate templates
std::map<std::string, CertificateInfo> getCertificateTemplates() {
    std::map<std::string, CertificateInfo> templates;

    // Great Patreon - Maximum features
    templates["great_patreon"] = {
        eCertGreatPatreon, eCertMaxLevel, "GREAT_PATREON", "ADVANCED",
        "SBOX,EBOX,NETI,DESK", 365, "", "", "Sandboxie-Plus", true
    };

    // Developer - Maximum features
    templates["developer"] = {
        eCertDeveloper, eCertMaxLevel, "DEVELOPER", "",
        "SBOX,EBOX,NETI,DESK", 365, "", "", "Sandboxie-Plus", false
    };

    // Eternal - Never expires
    templates["eternal"] = {
        eCertEternal, eCertMaxLevel, "ETERNAL", "",
        "SBOX,EBOX,NETI,DESK", -1, "", "", "Sandboxie-Plus", false
    };

    // Contributor - Maximum features
    templates["contributor"] = {
        eCertContributor, eCertMaxLevel, "CONTRIBUTOR", "",
        "SBOX,EBOX,NETI,DESK", 365, "", "", "Sandboxie-Plus", false
    };

    // Business - Advanced features
    templates["business"] = {
        eCertBusiness, eCertAdvanced, "BUSINESS", "ADVANCED",
        "SBOX,EBOX,NETI", 365, "", "", "Sandboxie-Plus", false
    };

    // Personal - Standard features
    templates["personal"] = {
        eCertPersonal, eCertStandard, "PERSONAL", "STANDARD",
        "SBOX", 365, "", "", "Sandboxie-Plus", false
    };

    // Home - Standard features
    templates["home"] = {
        eCertHome, eCertStandard, "HOME", "STANDARD",
        "SBOX", 365, "", "", "Sandboxie-Plus", false
    };

    // Evaluation - 30 days
    templates["evaluation"] = {
        eCertEvaluation, eCertMaxLevel, "EVALUATION", "",
        "SBOX,EBOX,NETI,DESK", 30, "", "", "Sandboxie-Plus", false
    };

    return templates;
}

// Generate certificate content
std::string generateCertificate(const CertificateInfo& info, const std::string& customHwid = "") {
    std::ostringstream cert;

    // Add BOM for UTF-16LE (Windows expects this)
    cert << "\xEF\xBB\xBF";

    // Software name
    cert << "SOFTWARE: " << info.software << "\n";

    // Date
    cert << "DATE: " << getCurrentDate();
    if (info.days > 0) {
        cert << " +" << info.days;
    }
    cert << "\n";

    // Type and Level
    if (!info.levelStr.empty()) {
        cert << "TYPE: " << info.typeStr << "-" << info.levelStr << "\n";
    } else {
        cert << "TYPE: " << info.typeStr << "\n";
    }

    // Options
    if (!info.options.empty()) {
        cert << "OPTIONS: " << info.options << "\n";
    }

    // HWID (if specified)
    std::string hwid = customHwid.empty() ? (info.locked ? generateHWID() : "") : customHwid;
    if (!hwid.empty()) {
        cert << "HWID: " << hwid << "\n";
    }

    // Update Key
    if (!info.updateKey.empty()) {
        cert << "UPDATEKEY: " << info.updateKey << "\n";
    } else {
        cert << "UPDATEKEY: " << generateUpdateKey() << "\n";
    }

    // Generate and add signature
    cert << "SIGNATURE: " << generateDummySignature() << "\n";

    return cert.str();
}

// Display available templates
void displayTemplates() {
    auto templates = getCertificateTemplates();

    std::cout << "\nAvailable Certificate Templates:\n";
    std::cout << "================================\n";

    for (const auto& pair : templates) {
        const auto& info = pair.second;
        std::cout << pair.first << " - " << info.typeStr;
        if (!info.levelStr.empty()) {
            std::cout << " (" << info.levelStr << ")";
        }
        std::cout << " - ";
        if (info.days == -1) {
            std::cout << "Never expires";
        } else {
            std::cout << info.days << " days";
        }
        std::cout << "\n";
    }
    std::cout << "\n";
}

// Save certificate to file
bool saveCertificate(const std::string& content, const std::string& filename) {
    std::ofstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Error: Could not create file " << filename << std::endl;
        return false;
    }

    file << content;
    file.close();

    std::cout << "Certificate saved to: " << filename << std::endl;
    return true;
}

// Display usage information
void displayUsage(const char* programName) {
    std::cout << "Sandboxie License Generator\n";
    std::cout << "===========================\n\n";
    std::cout << "Usage: " << programName << " [options]\n\n";
    std::cout << "Options:\n";
    std::cout << "  -t, --template <name>    Use predefined template (see --list)\n";
    std::cout << "  -o, --output <file>      Output filename (default: Certificate.dat)\n";
    std::cout << "  -h, --hwid <hwid>        Hardware ID for locked certificates\n";
    std::cout << "  -l, --list               List available templates\n";
    std::cout << "  --help                   Show this help message\n\n";
    std::cout << "Examples:\n";
    std::cout << "  " << programName << " --list\n";
    std::cout << "  " << programName << " -t great_patreon -o Certificate.dat\n";
    std::cout << "  " << programName << " -t business -h 1234567890ABCDEF\n\n";
}

int main(int argc, char* argv[]) {
    srand((unsigned int)time(nullptr));

    std::string templateName;
    std::string outputFile = "Certificate.dat";
    std::string hwid;
    bool listTemplates = false;

    // Parse command line arguments
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "--help") {
            displayUsage(argv[0]);
            return 0;
        }
        else if (arg == "-l" || arg == "--list") {
            listTemplates = true;
        }
        else if ((arg == "-t" || arg == "--template") && i + 1 < argc) {
            templateName = argv[++i];
        }
        else if ((arg == "-o" || arg == "--output") && i + 1 < argc) {
            outputFile = argv[++i];
        }
        else if ((arg == "-h" || arg == "--hwid") && i + 1 < argc) {
            hwid = argv[++i];
        }
        else {
            std::cerr << "Unknown argument: " << arg << std::endl;
            displayUsage(argv[0]);
            return 1;
        }
    }

    if (listTemplates) {
        displayTemplates();
        return 0;
    }

    if (templateName.empty()) {
        std::cout << "No template specified. Use --list to see available templates.\n\n";
        displayUsage(argv[0]);
        return 1;
    }

    auto templates = getCertificateTemplates();
    auto it = templates.find(templateName);
    if (it == templates.end()) {
        std::cerr << "Error: Template '" << templateName << "' not found.\n";
        displayTemplates();
        return 1;
    }

    std::cout << "Generating certificate with template: " << templateName << std::endl;

    std::string certificate = generateCertificate(it->second, hwid);

    if (saveCertificate(certificate, outputFile)) {
        std::cout << "Certificate generated successfully!\n";
        std::cout << "Template: " << it->second.typeStr;
        if (!it->second.levelStr.empty()) {
            std::cout << " (" << it->second.levelStr << ")";
        }
        std::cout << "\n";
        if (it->second.days == -1) {
            std::cout << "Validity: Never expires\n";
        } else {
            std::cout << "Validity: " << it->second.days << " days\n";
        }
        if (!hwid.empty()) {
            std::cout << "Hardware locked to: " << hwid << "\n";
        }
        return 0;
    }

    return 1;
}
