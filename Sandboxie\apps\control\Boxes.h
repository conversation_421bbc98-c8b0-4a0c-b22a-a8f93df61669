/*
 * Copyright 2004-2020 Sandboxie Holdings, LLC 
 *
 * This program is free software: you can redistribute it and/or modify
 *   it under the terms of the GNU General Public License as published by
 *   the Free Software Foundation, either version 3 of the License, or
 *   (at your option) any later version.
 *
 *   This program is distributed in the hope that it will be useful,
 *   but WITHOUT ANY WARRANTY; without even the implied warranty of
 *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *   GNU General Public License for more details.
 *
 *   You should have received a copy of the GNU General Public License
 *   along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */

//---------------------------------------------------------------------------
// Boxes
//---------------------------------------------------------------------------


#ifndef _MY_BOXES_H
#define _MY_BOXES_H


#include "Box.h"


class CBoxes : public CObArray
{
    static CBoxes *m_instance;

    CBoxes();

    void Clear();

    bool m_AnyHiddenBoxes;

public:

    static CString m_DefaultBox;

    ~CBoxes();

    static CBoxes &GetInstance();

    void ReloadBoxes();

    void RefreshProcesses();

    CBox &GetBox(int index) const;
    CBox &GetBox(const CString &name) const;
    CBox &GetBoxByProcessId(ULONG pid) const;
    int GetBoxIndex(const CString &name) const;

    BOOL AnyActivity() const;

    void KillAll() const;

    bool AnyHiddenBoxes() const;
    void GetHiddenBoxes(CStringList &list) const;

    void ReadExpandedView();
    BOOL WriteExpandedView();
};


#endif // _MY_BOXES_H
