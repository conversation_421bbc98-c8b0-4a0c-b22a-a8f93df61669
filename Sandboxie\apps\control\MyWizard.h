/*
 * Copyright 2004-2020 Sandboxie Holdings, LLC 
 *
 * This program is free software: you can redistribute it and/or modify
 *   it under the terms of the GNU General Public License as published by
 *   the Free Software Foundation, either version 3 of the License, or
 *   (at your option) any later version.
 *
 *   This program is distributed in the hope that it will be useful,
 *   but WITHOUT ANY WARRANTY; without even the implied warranty of
 *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *   GNU General Public License for more details.
 *
 *   You should have received a copy of the GNU General Public License
 *   along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */

//---------------------------------------------------------------------------
// My Generic Wizard
//---------------------------------------------------------------------------


#ifndef _MY_MYWIZARD_H
#define _MY_MYWIZARD_H


#include "apps/common/Layout.h"
#include "BaseDialog.h"


class CMyWizardPage : public CLayoutPropertyPage
{

protected:

    void SetPageTitle(const CString &text);
    void SetPageTitle(ULONG msgid);

    CStatic *CreateStatic(
        const CString &text, const CPoint &pos, const CPoint &size);
    CStatic *CreateStatic(
        ULONG msgid, const CPoint &pos, const CPoint &size);

public:

    CMyWizardPage(int page_num);

};


class CMyWizard : public CPropertySheet
{

protected:

    DECLARE_MESSAGE_MAP()

    CBaseDialog *m_pBaseDialog;

    CPtrList m_pages;

    ULONG m_title_msgid;

protected:

    void AddPage(CMyWizardPage *page);

    virtual BOOL OnInitDialog();

    afx_msg void OnSize(UINT nType, int cx, int cy);

    afx_msg void OnNcLButtonDown(UINT nHitTest, CPoint point);

public:

    CMyWizard(CWnd *pParentWnd, ULONG title_msgid);
    ~CMyWizard();

};


#endif // _MY_MYWIZARD_H
