//Microsoft Developer Studio generated resource script.
//

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "afxres.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// English (U.S.) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
#pragma code_page(1252)
#endif //_WIN32

#ifndef _MAC
/////////////////////////////////////////////////////////////////////////////
//
// Version
//

#include "common/my_version.h"

VS_VERSION_INFO VERSIONINFO
 FILEVERSION MY_VERSION_BINARY
 PRODUCTVERSION MY_VERSION_BINARY
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x40004L
 FILETYPE 0x2L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "Comments", "\0"
            VALUE "CompanyName", MY_COMPANY_NAME_STRING "\0"
            VALUE "FileDescription", MY_PRODUCT_NAME_STRING " COM Services (wuauserv)\0"
            VALUE "FileVersion", MY_VERSION_STRING "\0"
   OPTIONAL_VALUE("InternalName", "WUAU\0")
            VALUE "LegalCopyright", MY_COPYRIGHT_STRING "\0"
            VALUE "LegalTrademarks", "\0"
   OPTIONAL_VALUE("OriginalFilename", "SandboxieWUAU.exe\0")
            VALUE "PrivateBuild", "\0"
            VALUE "ProductName", MY_PRODUCT_NAME_STRING "\0"
            VALUE "ProductVersion", MY_VERSION_STRING "\0"
            VALUE "SpecialBuild", "\0"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END

#endif    // !_MAC

#endif    // English (U.S.) resources
/////////////////////////////////////////////////////////////////////////////
