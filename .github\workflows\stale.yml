name: "Mark stale issues and PRs"
 
on:
  workflow_dispatch:
  schedule:
  - cron: "0 0 * * *"

jobs:
  stale:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
    - uses: actions/stale@v9
      with:
        operations-per-run: 50
        days-before-stale: 90
        days-before-close: 14
        close-issue-reason: "not_planned"
        stale-issue-message: "As it has been 3 months since the last activity, we are automatically closing this issue in 14 days. If it is still present, please respond to help us investigate on newer Sandboxie versions. Thank you for your contribution!"
        stale-pr-message: "This pull request is stale because it has been open for 3 months with no activity. It will be closed in 14 days if no further activity occurs. Thank you for your contribution!"
        stale-issue-label: "stale"
        stale-pr-label: "stale"
        remove-stale-when-updated: true
        exempt-all-assignees: true
        any-of-issue-labels: "More Info Needed,Answered?,Fixed ???,Incorrect,Outdated Version"
        exempt-issue-labels: "Feature Request,Priority: Low,Type: Regression,Status: Stalled Work,Priority: High,ToDo,Status: Work in Progress,Workaround,Known Issue,Type: Bug,Type: Suggestions,Help Wanted,Type: Build Issue"
        any-of-pr-labels: "More Info Needed,Answered?,Incorrect"
        exempt-pr-labels: "Feature Request,Priority: Low,Type: Dependencies,Status: Work in Progress,Status: Stalled Work,Priority: High,ToDo,Help Wanted"
